{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">{{ title }}</h4>
            </div>
            <div class="card-body">
                <!-- Doctor Selection -->
                {% if not doctor %}
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Select Doctor</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                {% for doc in doctors %}
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100">
                                            <div class="card-body text-center">
                                                <h5 class="card-title">Dr. {{ doc.get_full_name }}</h5>
                                                <p class="card-text text-muted">{{ doc.profile.specialization|default:"General" }}</p>
                                                <a href="{% url 'appointments:manage_doctor_schedule' doctor_id=doc.id %}" class="btn btn-primary">
                                                    <i class="fas fa-calendar-alt"></i> Manage Schedule
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                {% endif %}
                
                <!-- Doctor Schedule Form -->
                {% if doctor %}
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Add/Update Schedule</h5>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                {% csrf_token %}
                                
                                {% if form.non_field_errors %}
                                    <div class="alert alert-danger">
                                        {{ form.non_field_errors }}
                                    </div>
                                {% endif %}
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.doctor.id_for_label }}" class="form-label">Doctor</label>
                                        {{ form.doctor|add_class:"form-select" }}
                                        {% if form.doctor.errors %}
                                            <div class="text-danger">
                                                {{ form.doctor.errors }}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.weekday.id_for_label }}" class="form-label">Day of Week</label>
                                        {{ form.weekday|add_class:"form-select" }}
                                        {% if form.weekday.errors %}
                                            <div class="text-danger">
                                                {{ form.weekday.errors }}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="{{ form.start_time.id_for_label }}" class="form-label">Start Time</label>
                                        {{ form.start_time|add_class:"form-control" }}
                                        {% if form.start_time.errors %}
                                            <div class="text-danger">
                                                {{ form.start_time.errors }}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="{{ form.end_time.id_for_label }}" class="form-label">End Time</label>
                                        {{ form.end_time|add_class:"form-control" }}
                                        {% if form.end_time.errors %}
                                            <div class="text-danger">
                                                {{ form.end_time.errors }}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="{{ form.is_available.id_for_label }}" class="form-label">Available</label>
                                        <div class="form-check form-switch mt-2">
                                            {{ form.is_available }}
                                            <label class="form-check-label" for="{{ form.is_available.id_for_label }}">
                                                Available for appointments
                                            </label>
                                        </div>
                                        {% if form.is_available.errors %}
                                            <div class="text-danger">
                                                {{ form.is_available.errors }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-end mt-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Save Schedule
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Current Schedule -->
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Current Schedule for Dr. {{ doctor.get_full_name }}</h5>
                        </div>
                        <div class="card-body">
                            {% if schedules %}
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Day</th>
                                                <th>Start Time</th>
                                                <th>End Time</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for schedule in schedules %}
                                                <tr>
                                                    <td>{{ schedule.get_weekday_display }}</td>
                                                    <td>{{ schedule.start_time|time:"h:i A" }}</td>
                                                    <td>{{ schedule.end_time|time:"h:i A" }}</td>
                                                    <td>
                                                        {% if schedule.is_available %}
                                                            <span class="badge bg-success">Available</span>
                                                        {% else %}
                                                            <span class="badge bg-danger">Not Available</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="{% url 'appointments:delete_doctor_schedule' schedule.id %}" class="btn btn-danger">
                                                                <i class="fas fa-trash"></i> Delete
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    No schedule has been set for Dr. {{ doctor.get_full_name }} yet.
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="{% url 'appointments:list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Appointments
                    </a>
                    {% if doctor %}
                        <a href="{% url 'appointments:manage_doctor_leaves' %}" class="btn btn-primary">
                            <i class="fas fa-calendar-times"></i> Manage Leaves
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize select2 for doctor dropdown
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
    });
</script>
{% endblock %}
