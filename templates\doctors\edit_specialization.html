{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Edit Specialization - Hospital Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Specialization</h1>
        <a href="{% url 'doctors:manage_specializations' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left mr-1"></i> Back to Specializations
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Edit Specialization: {{ specialization.name }}</h6>
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                
                <div class="mb-3">
                    <label for="{{ form.name.id_for_label }}" class="form-label">Specialization Name *</label>
                    {{ form.name|add_class:"form-control" }}
                    {% if form.name.errors %}
                        <div class="text-danger">{{ form.name.errors }}</div>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                    {{ form.description|add_class:"form-control" }}
                    {% if form.description.errors %}
                        <div class="text-danger">{{ form.description.errors }}</div>
                    {% endif %}
                </div>
                
                <div class="text-center">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i> Save Changes
                    </button>
                    <a href="{% url 'doctors:manage_specializations' %}" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
