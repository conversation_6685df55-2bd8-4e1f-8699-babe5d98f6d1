/* === HMS Standalone Sidebar CSS (Context7 MCP Best Practice) === */

/* 1. Sidebar Container */
#sidebar, .sidebar {
    width: 250px;
    min-width: 250px;
    max-width: 250px;
    height: 100vh;
    min-height: 100vh;
    background: #23272b;
    color: #fff;
    position: relative;
    left: 0;
    top: 0;
    margin: 0;
    padding: 0;
    z-index: 100;
    display: flex;
    flex-direction: column;
    font-family: 'Segoe UI', Arial, sans-serif;
    overflow-y: auto;
    background-attachment: local;
}

/* 2. Sidebar Branding */
.sidebar .sidebar-brand, #sidebar .sidebar-brand {
    padding: 24px 0 16px 0;
    text-align: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: #00bfff;
    letter-spacing: 1px;
}

/* 3. Sidebar Navigation */
.sidebar .nav, #sidebar .nav {
    flex: 1 1 0%;
    display: flex;
    flex-direction: column;
    padding: 0;
    margin: 0;
    list-style: none;
}
.sidebar .nav li, #sidebar .nav li {
    width: 100%;
}
.sidebar .nav a, #sidebar .nav a {
    color: #fff;
    text-decoration: none;
    padding: 12px 24px;
    display: block;
    transition: background 0.2s, color 0.2s;
    border-left: 4px solid transparent;
}
.sidebar .nav a.active, #sidebar .nav a.active, .sidebar .nav a:hover, #sidebar .nav a:hover {
    background: #1a1d21;
    color: #00bfff;
    border-left: 4px solid #00bfff;
}

/* 4. Sidebar Section Titles */
.sidebar .sidebar-heading, #sidebar .sidebar-heading {
    padding: 16px 24px 8px 24px;
    font-size: 0.85rem;
    text-transform: uppercase;
    color: #b0b3b8;
    letter-spacing: 1px;
    font-weight: 600;
}

/* 5. Sidebar Divider */
.sidebar hr, #sidebar hr {
    border: 0;
    border-top: 1px solid #444950;
    margin: 8px 0;
}

/* 6. Sidebar Footer */
.sidebar .sidebar-footer, #sidebar .sidebar-footer {
    padding: 16px 24px;
    font-size: 0.9rem;
    color: #b0b3b8;
    border-top: 1px solid #444950;
}

/* 7. Responsive: Stack Sidebar on Small Screens */
@media (max-width: 991.98px) {
    #sidebar, .sidebar {
        width: 100vw;
        min-width: 0;
        max-width: 100vw;
        height: auto;
        position: relative;
    }
}

/* 8. Accessibility: Focus Styles */
.sidebar a:focus, #sidebar a:focus {
    outline: 2px solid #00bfff;
    outline-offset: 2px;
}

/* Sidebar Styles */
.sidebar {
    width: 12rem; /* Reduced width to decrease the gap */
    min-height: 100vh;
    transition: all 0.3s;
    z-index: 1;
}

.sidebar .sidebar-brand {
    height: 4.375rem;
    text-decoration: none;
    font-size: 1rem;
    font-weight: 800;
    padding: 1.5rem 1rem;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
    z-index: 1;
    color: #ffffff;
}

.sidebar hr.sidebar-divider {
    margin: 0 1rem 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.15);
}

.sidebar .sidebar-heading {
    text-align: left;
    padding: 0 1rem;
    font-weight: 800;
    font-size: 0.65rem;
    text-transform: uppercase;
    letter-spacing: 0.13rem;
    color: rgba(255, 255, 255, 0.6);
}

.sidebar .nav-item {
    position: relative;
}

.sidebar .nav-item:last-child {
    margin-bottom: 1rem;
}

.sidebar .nav-item .nav-link {
    text-align: left;
    padding: 0.75rem 1rem;
    width: 14rem;
    font-weight: 500;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
}

.sidebar .nav-item .nav-link i {
    margin-right: 0.25rem;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
}

.sidebar .nav-item .nav-link:hover {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-item .nav-link:hover i {
    color: #ffffff;
}

.sidebar .nav-item .nav-link span {
    font-size: 0.85rem;
    display: inline;
}

.sidebar .nav-item.active .nav-link {
    font-weight: 700;
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.15);
}

.sidebar .nav-item.active .nav-link i {
    color: #ffffff;
}

.sidebar .nav-item .collapse {
    position: relative;
    left: 0;
    z-index: 1;
    top: 0;
    animation: none;
}

.sidebar .nav-item .collapse .collapse-inner {
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.sidebar .nav-item .collapsing {
    display: none;
    transition: none;
}

.sidebar .nav-item .collapse .collapse-inner,
.sidebar .nav-item .collapsing .collapse-inner {
    padding: 0.5rem 0;
    min-width: 10rem;
    font-size: 0.85rem;
    margin: 0 0 1rem 0;
}

.sidebar .nav-item .collapse .collapse-inner .collapse-header,
.sidebar .nav-item .collapsing .collapse-inner .collapse-header {
    margin: 0;
    white-space: nowrap;
    padding: 0.5rem 1.5rem;
    text-transform: uppercase;
    font-weight: 800;
    font-size: 0.65rem;
    color: #4e73df;
}

.sidebar .nav-item .collapse .collapse-inner .collapse-item,
.sidebar .nav-item .collapsing .collapse-inner .collapse-item {
    padding: 0.5rem 1rem;
    margin: 0 0.5rem;
    display: block;
    color: #3a3b45;
    text-decoration: none;
    border-radius: 0.35rem;
    white-space: nowrap;
}

.sidebar .nav-item .collapse .collapse-inner .collapse-item:hover,
.sidebar .nav-item .collapsing .collapse-inner .collapse-item:hover {
    background-color: #eaecf4;
}

.sidebar .nav-item .collapse .collapse-inner .collapse-item:active,
.sidebar .nav-item .collapsing .collapse-inner .collapse-item:active {
    background-color: #dddfeb;
}

.sidebar .nav-item .collapse .collapse-inner .collapse-item.active,
.sidebar .nav-item .collapsing .collapse-inner .collapse-item.active {
    color: #4e73df;
    font-weight: 700;
}

/* Sidebar Toggler */
.sidebar-toggled .sidebar {
    width: 0 !important;
    overflow: hidden;
}

.sidebar-toggled .sidebar .sidebar-brand .sidebar-brand-text {
    display: none;
}

.sidebar-toggled .sidebar .nav-item .nav-link {
    text-align: center;
    padding: 0.75rem 1rem;
    width: 6.5rem;
}

.sidebar-toggled .sidebar .nav-item .nav-link span {
    font-size: 0.65rem;
    display: block;
}

.sidebar-toggled .sidebar .nav-item .nav-link i {
    margin-right: 0;
    font-size: 1rem;
}

.sidebar-toggled .sidebar .nav-item .nav-link[data-bs-toggle="collapse"]::after {
    display: none;
}

.sidebar-toggled .sidebar .nav-item .collapse {
    position: absolute;
    left: calc(6.5rem + 1.5rem / 2);
    z-index: 1;
    top: 2px;
    animation-name: growIn;
    animation-duration: 200ms;
    animation-timing-function: transform cubic-bezier(0.18, 1.25, 0.4, 1), opacity cubic-bezier(0, 1, 0.4, 1);
}

.sidebar-toggled .sidebar .nav-item .collapse .collapse-inner {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 0.35rem;
}

.sidebar-toggled .sidebar .nav-item .collapsing {
    display: none;
    transition: none;
}

.sidebar-toggled .sidebar .nav-item:last-child {
    margin-bottom: 1rem;
}

.sidebar-toggled .sidebar .nav-item .collapse,
.sidebar-toggled .sidebar .nav-item .collapsing {
    margin: 0;
}

.sidebar-toggled .sidebar .nav-item .collapse .collapse-inner,
.sidebar-toggled .sidebar .nav-item .collapsing .collapse-inner {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 0.35rem;
}

.sidebar-toggled .sidebar .collapse::before {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    right: 100%;
    height: 100%;
    width: 1.5rem;
}

.sidebar-toggled .content {
    margin-left: 0 !important;
}

#sidebarToggle {
    width: 2.5rem;
    height: 2.5rem;
    text-align: center;
    margin-bottom: 1rem;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.8);
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

#sidebarToggle::after {
    font-weight: 900;
    content: '\f104';
    font-family: 'Font Awesome 5 Free';
    margin-right: 0.1rem;
}

#sidebarToggle:hover {
    background-color: rgba(255, 255, 255, 0.25);
    color: #ffffff;
}

.sidebar-toggled #sidebarToggle::after {
    content: '\f105';
    font-family: 'Font Awesome 5 Free';
    margin-left: 0.25rem;
}

/* Content Wrapper */
#content-wrapper {
    background-color: #f8f9fc;
    width: 100%;
    overflow-x: hidden;
    margin-left: 12rem; /* Adjusted to match the new sidebar width */
}

#content-wrapper #content {
    flex: 1 0 auto;
}

/* Page Wrapper */
#wrapper {
    display: flex;
    flex-direction: row;
    min-height: 100vh;
}

#wrapper #content-wrapper {
    width: 100%;
    overflow-x: hidden;
}

/* Content Wrapper adjustments for sidebar */
#wrapper #content-wrapper {
    /* Default state: sidebar is visible (14rem wide) */
    margin-left: 14rem; /* Push content to the right of the sidebar */
    width: calc(100% - 14rem); /* Adjust width to fill remaining space */
    transition: margin-left 0.3s, width 0.3s;
    overflow-x: hidden; /* Already present, good */
}

/* When sidebar is toggled (hidden) */
#wrapper.sidebar-toggled #content-wrapper {
    margin-left: 0;
    width: 100%;
}

/* Responsive adjustments for smaller screens where sidebar might behave differently */
@media (max-width: 767.98px) { /* Bootstrap 5 breakpoint for sm */
    #wrapper #content-wrapper {
        margin-left: 0; /* Sidebar is often overlay or fully hidden */
        width: 100%;
    }

    /* If sidebar is toggled to be visible on small screens as an overlay */
    /* This depends on your specific toggling JS and if it adds a class to #wrapper or body */
    /* Example: if .sidebar-toggled means sidebar is *shown* as overlay on mobile */
    /* #wrapper.sidebar-toggled-mobile #content-wrapper { */
        /* margin-left: 0; /* Content doesn't shift for overlay sidebar */
    /* } */
}

#wrapper #content-wrapper #content {
    flex: 1 0 auto;
}

/* Responsive Styles */
@media (min-width: 768px) {
    .sidebar {
        width: 14rem !important;
    }

    .sidebar .nav-item .collapse {
        position: relative;
        left: 0;
        z-index: 1;
        top: 0;
        animation: none;
    }

    .sidebar .nav-item .collapse .collapse-inner {
        border-radius: 0;
        box-shadow: none;
    }

    .sidebar .nav-item .collapsing {
        display: block;
        transition: height 0.15s ease;
    }

    .sidebar .nav-item .collapse,
    .sidebar .nav-item .collapsing {
        margin: 0 0 1rem 0;
    }

    .sidebar .nav-item .nav-link {
        display: block;
        width: 100%;
        text-align: left;
        padding: 1rem;
        width: 14rem;
    }

    .sidebar .nav-item .nav-link i {
        font-size: 0.85rem;
        margin-right: 0.25rem;
    }

    .sidebar .nav-item .nav-link span {
        font-size: 0.85rem;
        display: inline;
    }

    .sidebar .nav-item .nav-link[data-bs-toggle="collapse"]::after {
        width: 1rem;
        text-align: center;
        float: right;
        vertical-align: 0;
        border: 0;
        font-weight: 900;
        content: '\f107';
        font-family: 'Font Awesome 5 Free';
        color: rgba(255, 255, 255, 0.8);
    }

    .sidebar .nav-item .nav-link[data-bs-toggle="collapse"].collapsed::after {
        content: '\f105';
    }

    .sidebar .sidebar-brand .sidebar-brand-icon i {
        font-size: 2rem;
    }

    .sidebar .sidebar-brand .sidebar-brand-text {
        display: inline;
    }

    .sidebar .sidebar-heading {
        text-align: left;
    }

    .sidebar.toggled {
        overflow: visible;
        width: 6.5rem !important;
    }

    .sidebar.toggled .nav-item .collapse {
        position: absolute;
        left: calc(6.5rem + 1.5rem / 2);
        z-index: 1;
        top: 2px;
        animation-name: growIn;
        animation-duration: 200ms;
        animation-timing-function: transform cubic-bezier(0.18, 1.25, 0.4, 1), opacity cubic-bezier(0, 1, 0.4, 1);
    }

    .sidebar.toggled .nav-item .collapse .collapse-inner {
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        border-radius: 0.35rem;
    }

    .sidebar.toggled .nav-item .collapsing {
        display: none;
        transition: none;
    }

    .sidebar.toggled .nav-item:last-child {
        margin-bottom: 1rem;
    }

    .sidebar.toggled .nav-item .nav-link {
        text-align: center;
        padding: 0.75rem 1rem;
        width: 6.5rem;
    }

    .sidebar.toggled .nav-item .nav-link span {
        font-size: 0.65rem;
        display: block;
    }

    .sidebar.toggled .nav-item .nav-link i {
        margin-right: 0;
        font-size: 1rem;
    }

    .sidebar.toggled .nav-item .nav-link[data-bs-toggle="collapse"]::after {
        display: none;
    }

    .sidebar.toggled .sidebar-brand .sidebar-brand-icon i {
        font-size: 2rem;
    }

    .sidebar.toggled .sidebar-brand .sidebar-brand-text {
        display: none;
    }

    .sidebar.toggled .sidebar-heading {
        text-align: center;
    }
}

/* Topbar Styles */
.topbar {
    height: 4.375rem;
}

.topbar #sidebarToggleTop {
    height: 2.5rem;
    width: 2.5rem;
}

.topbar #sidebarToggleTop:hover {
    background-color: #eaecf4;
}

.topbar #sidebarToggleTop:active {
    background-color: #dddfeb;
}

.topbar .navbar-search {
    width: 25rem;
}

.topbar .navbar-search input {
    font-size: 0.85rem;
    height: auto;
}

.topbar .topbar-divider {
    width: 0;
    border-right: 1px solid #e3e6f0;
    height: calc(4.375rem - 2rem);
    margin: auto 1rem;
}

.topbar .nav-item .nav-link {
    height: 4.375rem;
    display: flex;
    align-items: center;
    padding: 0 0.75rem;
}

.topbar .nav-item .nav-link:focus {
    outline: none;
}

.topbar .nav-item:focus {
    outline: none;
}

.topbar .dropdown {
    position: static;
}

.topbar .dropdown .dropdown-menu {
    width: calc(100% - 1.5rem);
    right: 0.75rem;
}

.topbar .dropdown-list {
    padding: 0;
    border: none;
    overflow: hidden;
}

.topbar .dropdown-list .dropdown-header {
    background-color: #4e73df;
    border: 1px solid #4e73df;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    color: #fff;
}

.topbar .dropdown-list .dropdown-item {
    white-space: normal;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    border-left: 1px solid #e3e6f0;
    border-right: 1px solid #e3e6f0;
    border-bottom: 1px solid #e3e6f0;
    line-height: 1.3rem;
}

.topbar .dropdown-list .dropdown-item .dropdown-list-image {
    position: relative;
    height: 2.5rem;
    width: 2.5rem;
}

.topbar .dropdown-list .dropdown-item .dropdown-list-image img {
    height: 2.5rem;
    width: 2.5rem;
}

.topbar .dropdown-list .dropdown-item .dropdown-list-image .status-indicator {
    background-color: #eaecf4;
    height: 0.75rem;
    width: 0.75rem;
    border-radius: 100%;
    position: absolute;
    bottom: 0;
    right: 0;
    border: 0.125rem solid #fff;
}

.topbar .dropdown-list .dropdown-item .text-truncate {
    max-width: 10rem;
}

.topbar .dropdown-list .dropdown-item:active {
    background-color: #eaecf4;
    color: #3a3b45;
}

@media (min-width: 576px) {
    .topbar .dropdown {
        position: relative;
    }
    .topbar .dropdown .dropdown-menu {
        width: auto;
        right: 0;
    }
    .topbar .dropdown-list {
        width: 20rem !important;
    }
    .topbar .dropdown-list .dropdown-item .text-truncate {
        max-width: 13.375rem;
    }
}

.topbar.navbar-dark .navbar-nav .nav-item .nav-link {
    color: rgba(255, 255, 255, 0.8);
}

.topbar.navbar-dark .navbar-nav .nav-item .nav-link:hover {
    color: #fff;
}

.topbar.navbar-dark .navbar-nav .nav-item .nav-link:active {
    color: #fff;
}

.topbar.navbar-light .navbar-nav .nav-item .nav-link {
    color: #d1d3e2;
}

.topbar.navbar-light .navbar-nav .nav-item .nav-link:hover {
    color: #b7b9cc;
}

.topbar.navbar-light .navbar-nav .nav-item .nav-link:active {
    color: #858796;
}

/* Main Content Styles */
.container-fluid {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

/* Animations */
@keyframes growIn {
    0% {
        transform: scale(0.9);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.animated--grow-in {
    animation-name: growIn;
    animation-duration: 200ms;
    animation-timing-function: transform cubic-bezier(0.18, 1.25, 0.4, 1), opacity cubic-bezier(0, 1, 0.4, 1);
}

/* Gradient Colors */
.bg-gradient-primary {
    background-color: #4e73df;
    background-image: linear-gradient(180deg, #4e73df 10%, #224abe 100%);
    background-size: cover;
    min-height: 100vh !important;
    height: 100vh !important;
    background-attachment: local !important;
}

/* Utilities */
.img-profile {
    height: 2rem;
    width: 2rem;
}

/* Responsive Adjustments */
@media (min-width: 768px) {
    .content {
        margin-left: 14rem;
        transition: margin 0.3s;
    }

    .sidebar-toggled .content {
        margin-left: 6.5rem;
    }
}

@media (max-width: 767.98px) {
    .sidebar {
        width: 0 !important;
    }

    .sidebar.toggled {
        width: 14rem !important;
    }

    .content {
        margin-left: 0;
    }

    .sidebar-toggled .content {
        margin-left: 0;
    }
}

/* Footer Styles */
.sticky-footer {
    padding: 2rem 0;
    flex-shrink: 0;
}

.sticky-footer .copyright {
    line-height: 1;
    font-size: 0.8rem;
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    right: 1rem;
    bottom: 1rem;
    display: none;
    width: 2.75rem;
    height: 2.75rem;
    text-align: center;
    color: #fff;
    background: rgba(90, 92, 105, 0.5);
    line-height: 46px;
    border-radius: 0.35rem;
}

.scroll-to-top:focus, .scroll-to-top:hover {
    color: white;
}

.scroll-to-top:hover {
    background: #5a5c69;
}

.scroll-to-top i {
    font-weight: 800;
}

/* Ensure sidebar and content are side by side, and content fills remaining space */
#wrapper {
    display: flex;
    flex-direction: row;
    min-height: 100vh;
}
#content-wrapper {
    flex-grow: 1;
    width: 100%;
    min-width: 0;
    background: #f8f9fc;
    overflow-y: auto;
    height: 100vh;
}
#sidebar {
    min-width: 220px;
    max-width: 250px;
    background: #222e3c;
    min-height: 100vh;
    height: 100vh;
    overflow-y: auto;
    background-attachment: local;
}
#content {
    width: 100%;
}

/* Strongly enforce sidebar/content layout for all screen sizes */
#wrapper {
    display: flex !important;
    flex-direction: row !important;
    min-height: 100vh;
    width: 100vw;
}
#sidebar {
    min-width: 220px;
    max-width: 250px;
    background: #222e3c;
    min-height: 100vh;
    height: 100vh;
    flex-shrink: 0;
    overflow-y: auto;
    background-attachment: local;
}
#content-wrapper {
    flex-grow: 1;
    width: 100%;
    min-width: 0;
    background: #f8f9fc;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    height: 100vh;
}
#content {
    width: 100%;
    flex: 1 1 auto;
}
@media (max-width: 991px) {
    #wrapper {
        flex-direction: column !important;
    }
    #sidebar {
        min-width: 100vw;
        max-width: 100vw;
        min-height: 100vh;
        height: 100vh;
        overflow-y: auto;
    }
}
