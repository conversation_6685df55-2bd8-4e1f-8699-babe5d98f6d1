{% extends 'base.html' %}
{% load form_tags %}
{% load billing_tags %}

{% block title %}Manage Services - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Services</h4>
            </div>
            <div class="card-body">
                {% if services %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Price</th>
                                    <th>Description</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for service in services %}
                                    <tr>
                                        <td>{{ service.name }}</td>
                                        <td>{{ service.get_category_display }}</td>
                                        <td>{{ service.price|currency }}</td>
                                        <td>{{ service.description|truncatechars:50 }}</td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{% url 'billing:edit_service' service.id %}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-edit"></i> Edit
                                                </a>
                                                <a href="{% url 'billing:delete_service' service.id %}" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i> Delete
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No services found. Use the form on the right to add a new service.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Add New Service</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">Service Name</label>
                        {{ form.name|add_class:"form-control" }}
                        {% if form.name.errors %}
                            <div class="text-danger">
                                {{ form.name.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.category.id_for_label }}" class="form-label">Category</label>
                        {{ form.category|add_class:"form-control" }}
                        {% if form.category.errors %}
                            <div class="text-danger">
                                {{ form.category.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.price.id_for_label }}" class="form-label">Price</label>
                        {{ form.price|add_class:"form-control" }}
                        {% if form.price.errors %}
                            <div class="text-danger">
                                {{ form.price.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                        {{ form.description|add_class:"form-control" }}
                        {% if form.description.errors %}
                            <div class="text-danger">
                                {{ form.description.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus-circle me-1"></i> Add Service
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
