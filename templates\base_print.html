<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Hospital Management System{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <style>
        @media print {
            body {
                font-size: 12pt;
                line-height: 1.3;
            }
            
            .container {
                width: 100%;
                max-width: 100%;
            }
            
            .no-print {
                display: none !important;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            table {
                width: 100%;
                border-collapse: collapse;
            }
            
            table, th, td {
                border: 1px solid #000;
            }
            
            th, td {
                padding: 8px;
                text-align: left;
            }
        }
        
        /* Print button styles */
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Print Button (only visible on screen) -->
    <button class="btn btn-primary print-button no-print" onclick="window.print()">
        <i class="fas fa-print"></i> Print
    </button>
    
    {% block content %}{% endblock %}
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    {% block extra_js %}{% endblock %}
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-print when the page loads (uncomment if needed)
            // window.print();
        });
    </script>
</body>
</html>
