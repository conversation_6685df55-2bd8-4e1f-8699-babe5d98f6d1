{% extends 'base.html' %}

{% block title %}Delete Medication - Hospital Management System{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">Delete Medication</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    You are about to deactivate the following medication:
                </div>
                
                <div class="text-center mb-4">
                    <i class="fas fa-pills fa-5x text-danger mb-3"></i>
                    <h5>{{ medication.name }}</h5>
                    <p class="text-muted">
                        {{ medication.generic_name }} - {{ medication.dosage_form }} - {{ medication.strength }}
                    </p>
                    <p class="text-muted">
                        <strong>Current Stock:</strong> {{ medication.stock_quantity }} units
                    </p>
                </div>
                
                <p class="mb-4">
                    This action will deactivate the medication. The medication will still exist in the database but will be marked as inactive and will not be available for prescriptions or purchases.
                    This action can be reversed by editing the medication and marking it as active again.
                </p>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'pharmacy:medication_detail' medication.id %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> No, Go Back
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Yes, Deactivate Medication
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
