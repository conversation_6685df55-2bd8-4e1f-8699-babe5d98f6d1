{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">{{ title }}</h4>
            </div>
            <div class="card-body">
                <!-- Leave Request Form -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Request Leave</h5>
                    </div>
                    <div class="card-body">
                        <form method="post">
                            {% csrf_token %}
                            
                            {% if form.non_field_errors %}
                                <div class="alert alert-danger">
                                    {{ form.non_field_errors }}
                                </div>
                            {% endif %}
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.doctor.id_for_label }}" class="form-label">Doctor</label>
                                    {{ form.doctor|add_class:"form-select select2" }}
                                    {% if form.doctor.errors %}
                                        <div class="text-danger">
                                            {{ form.doctor.errors }}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <label for="{{ form.start_date.id_for_label }}" class="form-label">Start Date</label>
                                    {{ form.start_date|add_class:"form-control" }}
                                    {% if form.start_date.errors %}
                                        <div class="text-danger">
                                            {{ form.start_date.errors }}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <label for="{{ form.end_date.id_for_label }}" class="form-label">End Date</label>
                                    {{ form.end_date|add_class:"form-control" }}
                                    {% if form.end_date.errors %}
                                        <div class="text-danger">
                                            {{ form.end_date.errors }}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-9 mb-3">
                                    <label for="{{ form.reason.id_for_label }}" class="form-label">Reason</label>
                                    {{ form.reason|add_class:"form-control" }}
                                    {% if form.reason.errors %}
                                        <div class="text-danger">
                                            {{ form.reason.errors }}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <label for="{{ form.is_approved.id_for_label }}" class="form-label">Approval Status</label>
                                    <div class="form-check form-switch mt-2">
                                        {{ form.is_approved }}
                                        <label class="form-check-label" for="{{ form.is_approved.id_for_label }}">
                                            Approved
                                        </label>
                                    </div>
                                    {% if form.is_approved.errors %}
                                        <div class="text-danger">
                                            {{ form.is_approved.errors }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-end mt-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Submit Leave Request
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Leave Requests List -->
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Leave Requests</h5>
                    </div>
                    <div class="card-body">
                        {% if leaves %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Doctor</th>
                                            <th>Start Date</th>
                                            <th>End Date</th>
                                            <th>Duration</th>
                                            <th>Reason</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for leave in leaves %}
                                            <tr>
                                                <td>Dr. {{ leave.doctor.get_full_name }}</td>
                                                <td>{{ leave.start_date|date:"M d, Y" }}</td>
                                                <td>{{ leave.end_date|date:"M d, Y" }}</td>
                                                <td>
                                                    {% with days=leave.end_date|timeuntil:leave.start_date %}
                                                        {% if days %}
                                                            {{ days }}
                                                        {% else %}
                                                            1 day
                                                        {% endif %}
                                                    {% endwith %}
                                                </td>
                                                <td>{{ leave.reason|truncatechars:30 }}</td>
                                                <td>
                                                    {% if leave.is_approved %}
                                                        <span class="badge bg-success">Approved</span>
                                                    {% else %}
                                                        <span class="badge bg-warning">Pending</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        {% if not leave.is_approved %}
                                                            <a href="{% url 'appointments:approve_doctor_leave' leave.id %}" class="btn btn-success">
                                                                <i class="fas fa-check"></i> Approve
                                                            </a>
                                                        {% endif %}
                                                        <a href="{% url 'appointments:delete_doctor_leave' leave.id %}" class="btn btn-danger">
                                                            <i class="fas fa-trash"></i> Delete
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                No leave requests found.
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{% url 'appointments:list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Appointments
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize select2 for doctor dropdown
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
    });
</script>
{% endblock %}
