{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Delete Medical History - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">Delete Medical History</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    You are about to delete the following medical history record for patient <strong>{{ patient.get_full_name }}</strong>.
                    This action cannot be undone.
                </div>
                
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">{{ history.diagnosis }}</h5>
                        <small class="text-muted">Date: {{ history.date|date:"F d, Y" }}</small>
                    </div>
                    <div class="card-body">
                        <p><strong>Treatment:</strong> {{ history.treatment }}</p>
                        {% if history.notes %}
                            <p><strong>Notes:</strong> {{ history.notes }}</p>
                        {% endif %}
                        <p><strong>Doctor:</strong> {{ history.doctor_name }}</p>
                    </div>
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'patients:medical_history' patient.id %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i> Delete Record
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
