/* Custom CSS for Hospital Management System */

/* General Styles */
body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.container {
    flex: 1;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    height: 100vh;
    min-height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    background: linear-gradient(180deg, var(--sidebar-bg) 0%, var(--sidebar-dark) 100%);
    color: var(--text-light);
    padding-top: var(--spacing-lg);
    transition: all var(--transition-speed);
    z-index: 1000;
    overflow-y: auto;
    box-shadow: var(--shadow-md);
    background-attachment: local;
}

.sidebar-animating {
    animation: sidebar-pulse 0.3s ease-in-out;
}

@keyframes sidebar-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(0.98); }
    100% { transform: scale(1); }
}

.sidebar-header {
    padding: 0 var(--spacing-lg) var(--spacing-lg) var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: var(--spacing-md);
}

.sidebar-brand {
    color: var(--text-light);
    font-size: 1.5rem;
    font-weight: 800;
    text-decoration: none;
    display: flex;
    align-items: center;
    letter-spacing: 0.5px;
}

.sidebar-brand i {
    font-size: 1.75rem;
    margin-right: var(--spacing-sm);
    color: var(--text-light);
}

.sidebar-brand:hover {
    color: var(--text-light);
    text-decoration: none;
    transform: scale(1.05);
    transition: transform 0.2s ease;
}

.sidebar-nav {
    padding: 0;
    list-style: none;
    margin: var(--spacing-md) 0;
}

.sidebar-nav .nav-item {
    margin-bottom: var(--spacing-xs);
    position: relative;
}

.sidebar-nav .nav-link {
    color: var(--sidebar-item);
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    align-items: center;
    border-radius: var(--border-radius-sm);
    margin: 0 var(--spacing-sm);
    transition: all var(--transition-speed);
    position: relative;
    font-weight: 600;
    letter-spacing: 0.3px;
}

.sidebar-nav .nav-link:hover {
    color: var(--sidebar-item-hover);
    background-color: var(--sidebar-item-active-bg);
    transform: translateX(5px);
}

.sidebar-nav .nav-link.active {
    color: var(--text-light);
    background-color: var(--primary-dark);
    box-shadow: var(--shadow-sm);
}

.sidebar-nav .nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background-color: var(--text-light);
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
}

.sidebar-nav .nav-link i {
    margin-right: var(--spacing-md);
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
    transition: all var(--transition-speed);
}

.sidebar-nav .dropdown-menu {
    background-color: var(--sidebar-dropdown-bg);
    border: none;
    margin-left: 0;
    margin-top: var(--spacing-xs);
    margin-bottom: var(--spacing-xs);
    padding: var(--spacing-xs) 0;
    position: static;
    float: none;
    width: 100%;
    box-shadow: none;
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-speed);
    overflow: hidden;
}

.sidebar-nav .dropdown-item {
    color: var(--sidebar-item);
    padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-sm) calc(var(--spacing-lg) * 2.5);
    font-size: 0.9rem;
    transition: all var(--transition-speed);
    position: relative;
}

.sidebar-nav .dropdown-item:hover {
    color: var(--sidebar-item-hover);
    background-color: var(--sidebar-item-active-bg);
    transform: translateX(5px);
}

.sidebar-nav .dropdown-item.active {
    color: var(--text-light);
    background-color: var(--primary-dark);
    font-weight: 600;
}

.sidebar-nav .dropdown-item.active::before {
    content: '';
    position: absolute;
    left: var(--spacing-lg);
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--text-light);
}

.sidebar-nav .dropdown-toggle::after {
    position: absolute;
    right: var(--spacing-lg);
    top: 50%;
    transform: translateY(-50%);
    transition: transform var(--transition-speed);
}

.sidebar-nav .dropdown-toggle[aria-expanded="true"]::after {
    transform: translateY(-50%) rotate(180deg);
}

.sidebar-nav .dropdown-divider {
    border-color: rgba(255, 255, 255, 0.1);
    margin: var(--spacing-xs) var(--spacing-md);
}

.sidebar-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    bottom: 0;
    background: linear-gradient(0deg, var(--sidebar-dark) 0%, var(--sidebar-bg) 100%);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    margin-top: auto;
}

.sidebar-footer .user-info {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-footer .user-info i {
    font-size: 1.5rem;
    margin-right: var(--spacing-sm);
    color: var(--text-light);
    background-color: var(--sidebar-item-active-bg);
    padding: var(--spacing-xs);
    border-radius: 50%;
}

.sidebar-footer .user-info span {
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-footer .btn-outline-light {
    border-color: rgba(255, 255, 255, 0.2);
    transition: all var(--transition-speed);
}

.sidebar-footer .btn-outline-light:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
}

/* Topbar Styles */
.topbar {
    height: 60px;
    background-color: var(--light);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    padding: 0 var(--spacing-lg);
    position: sticky;
    top: 0;
    z-index: 900;
    transition: all var(--transition-speed);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--primary);
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    margin-right: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-speed);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.sidebar-toggle:hover {
    background-color: var(--light-dark);
    color: var(--primary-dark);
    transform: rotate(90deg);
}

/* Breadcrumbs */
.breadcrumb {
    margin-bottom: 0;
    background-color: transparent;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: '›';
    color: var(--text-muted);
    font-size: 1.2rem;
    line-height: 1;
    vertical-align: middle;
}

.breadcrumb-item a {
    color: var(--text-muted);
    text-decoration: none;
    transition: color var(--transition-speed);
}

.breadcrumb-item a:hover {
    color: var(--primary);
}

.breadcrumb-item.active {
    color: var(--text-dark);
    font-weight: 600;
}

/* Collapsed Sidebar */
body.sidebar-collapsed .sidebar {
    width: 70px;
}

body.sidebar-collapsed .sidebar .sidebar-brand span,
body.sidebar-collapsed .sidebar .nav-link span,
body.sidebar-collapsed .sidebar-footer,
body.sidebar-collapsed .sidebar .dropdown-menu,
body.sidebar-collapsed .sidebar .dropdown-toggle::after {
    display: none;
}

body.sidebar-collapsed .content-wrapper {
    margin-left: 70px;
}

body.sidebar-collapsed .sidebar-nav .nav-link {
    padding: var(--spacing-md);
    justify-content: center;
    margin: var(--spacing-xs) auto;
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

body.sidebar-collapsed .sidebar-nav .nav-link i {
    margin-right: 0;
    font-size: 1.2rem;
}

body.sidebar-collapsed .sidebar-nav .nav-link.active::before {
    display: none;
}

body.sidebar-collapsed .sidebar-header {
    padding: var(--spacing-md) 0;
    text-align: center;
    display: flex;
    justify-content: center;
}

body.sidebar-collapsed .sidebar-brand {
    justify-content: center;
}

body.sidebar-collapsed .sidebar-brand i {
    margin-right: 0;
    font-size: 1.5rem;
}

/* Mobile Sidebar */
@media (max-width: 768px) {
    .sidebar {
        margin-left: -250px;
        box-shadow: var(--shadow-lg);
    }

    body.sidebar-open .sidebar {
        margin-left: 0;
    }

    .content-wrapper {
        margin-left: 0 !important;
        padding: var(--spacing-md);
    }

    .sidebar-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
        backdrop-filter: blur(2px);
        transition: opacity var(--transition-speed);
        opacity: 0;
    }

    body.sidebar-open .sidebar-overlay {
        display: block;
        opacity: 1;
    }

    .topbar {
        padding: 0 var(--spacing-md);
    }

    /* Adjust card styles for mobile */
    .card {
        margin-bottom: var(--spacing-md);
    }

    /* Make tables responsive on mobile */
    .table-responsive {
        border-radius: var(--border-radius-sm);
        box-shadow: var(--shadow-sm);
    }
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-speed);
    margin-bottom: var(--spacing-lg);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--light);
    border-bottom: 1px solid var(--light-dark);
    padding: var(--spacing-md) var(--spacing-lg);
    font-weight: 600;
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    background-color: var(--light);
    border-top: 1px solid var(--light-dark);
    padding: var(--spacing-md) var(--spacing-lg);
}

/* Button Styles */
.btn {
    border-radius: var(--border-radius-sm);
    padding: 0.375rem 1rem;
    font-weight: 600;
    transition: all var(--transition-speed);
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.btn-secondary {
    background-color: var(--secondary);
    border-color: var(--secondary);
}

.btn-secondary:hover, .btn-secondary:focus {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

/* Form Styles */
.form-control {
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--light-dark);
    padding: 0.5rem 1rem;
    transition: all var(--transition-speed);
}

.form-control:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
}

.alert-primary {
    background-color: rgba(78, 115, 223, 0.15);
    color: var(--primary-dark);
}

.alert-success {
    background-color: rgba(28, 200, 138, 0.15);
    color: var(--secondary-dark);
}

.alert-danger {
    background-color: rgba(231, 74, 59, 0.15);
    color: var(--danger);
}

.alert-warning {
    background-color: rgba(246, 194, 62, 0.15);
    color: #856404;
}

/* Navbar Styles */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Card Styles */
.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

/* Dashboard Icons */
.card i.fas {
    color: #007bff;
}

/* Form Styles */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
}

/* Table Styles */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

/* Button Styles */
.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

/* Footer Styles */
footer {
    margin-top: auto;
}

footer a {
    text-decoration: none;
}

footer a:hover {
    text-decoration: underline;
}

/* Login/Register Forms */
.auth-form {
    max-width: 450px;
    margin: 0 auto;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Patient Profile */
.patient-profile-img {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 50%;
    border: 5px solid #f8f9fa;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Dashboard Stats */
.stats-card {
    border-left: 4px solid #007bff;
}

.stats-icon {
    font-size: 2rem;
    opacity: 0.8;
}

/* Calendar */
.calendar-day {
    height: 120px;
    overflow-y: auto;
}

.calendar-event {
    padding: 2px 5px;
    margin-bottom: 2px;
    border-radius: 3px;
    font-size: 0.8rem;
    background-color: rgba(0, 123, 255, 0.1);
    border-left: 3px solid #007bff;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .patient-profile-img {
        width: 100px;
        height: 100px;
    }

    .calendar-day {
        height: 80px;
    }
}
