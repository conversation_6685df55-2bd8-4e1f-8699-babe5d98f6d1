{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Purchase Details - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Purchase Details</h4>
            </div>
            <div class="card-body">
                <!-- Purchase Information -->
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="border-bottom pb-2 mb-3">Purchase Information</h5>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Purchase ID:</div>
                            <div class="col-md-8">{{ purchase.id }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Invoice Number:</div>
                            <div class="col-md-8">{{ purchase.invoice_number }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Purchase Date:</div>
                            <div class="col-md-8">{{ purchase.purchase_date|date:"F d, Y" }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Total Amount:</div>
                            <div class="col-md-8">₦{{ purchase.total_amount }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Payment Status:</div>
                            <div class="col-md-8">
                                {% if purchase.payment_status == 'paid' %}
                                    <span class="badge bg-success">Paid</span>
                                {% elif purchase.payment_status == 'partial' %}
                                    <span class="badge bg-warning">Partial</span>
                                {% elif purchase.payment_status == 'pending' %}
                                    <span class="badge bg-danger">Pending</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Created By:</div>
                            <div class="col-md-8">{{ purchase.created_by.get_full_name }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Notes:</div>
                            <div class="col-md-8">{{ purchase.notes|default:"No notes provided." }}</div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h5 class="border-bottom pb-2 mb-3">Supplier Information</h5>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Supplier:</div>
                            <div class="col-md-8">{{ purchase.supplier.name }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Contact Person:</div>
                            <div class="col-md-8">{{ purchase.supplier.contact_person }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Email:</div>
                            <div class="col-md-8">{{ purchase.supplier.email }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Phone:</div>
                            <div class="col-md-8">{{ purchase.supplier.phone_number }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Address:</div>
                            <div class="col-md-8">
                                {{ purchase.supplier.address }}<br>
                                {{ purchase.supplier.city }}, {{ purchase.supplier.state }} {{ purchase.supplier.postal_code }}<br>
                                {{ purchase.supplier.country }}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Purchase Items -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="border-bottom pb-2 mb-0">Purchase Items</h5>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addItemModal">
                                <i class="fas fa-plus"></i> Add Item
                            </button>
                        </div>
                        
                        {% if purchase_items %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Medication</th>
                                            <th>Quantity</th>
                                            <th>Unit Price</th>
                                            <th>Total Price</th>
                                            <th>Batch Number</th>
                                            <th>Expiry Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in purchase_items %}
                                            <tr>
                                                <td>
                                                    <a href="{% url 'pharmacy:medication_detail' item.medication.id %}">
                                                        {{ item.medication.name }}
                                                    </a>
                                                    <div class="small text-muted">{{ item.medication.strength }} - {{ item.medication.dosage_form }}</div>
                                                </td>
                                                <td>{{ item.quantity }}</td>
                                                <td>₦{{ item.unit_price }}</td>
                                                <td>₦{{ item.total_price }}</td>
                                                <td>{{ item.batch_number }}</td>
                                                <td>
                                                    {{ item.expiry_date|date:"M d, Y" }}
                                                </td>
                                                <td>
                                                    <a href="{% url 'pharmacy:delete_purchase_item' item.id %}" class="btn btn-sm btn-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-primary">
                                            <th colspan="3" class="text-end">Total:</th>
                                            <th>₦{{ purchase.total_amount }}</th>
                                            <th colspan="3"></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                No items have been added to this purchase yet.
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{% url 'pharmacy:manage_purchases' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Purchases
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Add Item Modal -->
<div class="modal fade" id="addItemModal" tabindex="-1" aria-labelledby="addItemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addItemModalLabel">Add Purchase Item</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post">
                {% csrf_token %}
                <input type="hidden" name="add_item" value="1">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="{{ item_form.medication.id_for_label }}" class="form-label">Medication</label>
                            {{ item_form.medication|add_class:"form-select select2" }}
                            {% if item_form.medication.errors %}
                                <div class="text-danger">
                                    {{ item_form.medication.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ item_form.quantity.id_for_label }}" class="form-label">Quantity</label>
                            {{ item_form.quantity|add_class:"form-control" }}
                            {% if item_form.quantity.errors %}
                                <div class="text-danger">
                                    {{ item_form.quantity.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ item_form.unit_price.id_for_label }}" class="form-label">Unit Price (₦)</label>
                            {{ item_form.unit_price|add_class:"form-control" }}
                            {% if item_form.unit_price.errors %}
                                <div class="text-danger">
                                    {{ item_form.unit_price.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ item_form.batch_number.id_for_label }}" class="form-label">Batch Number</label>
                            {{ item_form.batch_number|add_class:"form-control" }}
                            {% if item_form.batch_number.errors %}
                                <div class="text-danger">
                                    {{ item_form.batch_number.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            <label for="{{ item_form.expiry_date.id_for_label }}" class="form-label">Expiry Date</label>
                            {{ item_form.expiry_date|add_class:"form-control" }}
                            {% if item_form.expiry_date.errors %}
                                <div class="text-danger">
                                    {{ item_form.expiry_date.errors }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Item</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize select2 for medication dropdown
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%',
            dropdownParent: $('#addItemModal')
        });
        
        // Calculate total price when quantity or unit price changes
        $('#{{ item_form.quantity.id_for_label }}, #{{ item_form.unit_price.id_for_label }}').on('input', function() {
            var quantity = parseFloat($('#{{ item_form.quantity.id_for_label }}').val()) || 0;
            var unitPrice = parseFloat($('#{{ item_form.unit_price.id_for_label }}').val()) || 0;
            var totalPrice = quantity * unitPrice;
            
            // Display total price somewhere if needed
            console.log('Total Price: $' + totalPrice.toFixed(2));
        });
    });
</script>
{% endblock %}
