{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Edit Doctor - Hospital Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Doctor</h1>
        <a href="{% url 'doctors:manage_doctors' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left mr-1"></i> Back to Doctors
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Edit Doctor: {{ doctor.get_full_name }}</h6>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="text-gray-800 mb-3">Account Information</h5>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="first_name" class="form-label">First Name *</label>
                        <input type="text" class="form-control" id="first_name" name="first_name" value="{{ user.first_name }}" required>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="last_name" class="form-label">Last Name *</label>
                        <input type="text" class="form-control" id="last_name" name="last_name" value="{{ user.last_name }}" required>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="email" class="form-label">Email *</label>
                        <input type="email" class="form-control" id="email" name="email" value="{{ user.email }}" required>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="phone_number" class="form-label">Phone Number *</label>
                        <input type="text" class="form-control" id="phone_number" name="phone_number" value="{{ user.profile.phone_number }}" required>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="text-gray-800 mb-3">Professional Information</h5>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="{{ doctor_form.specialization.id_for_label }}" class="form-label">Specialization *</label>
                        {{ doctor_form.specialization|add_class:"form-select" }}
                        {% if doctor_form.specialization.errors %}
                            <div class="text-danger">{{ doctor_form.specialization.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="{{ doctor_form.department.id_for_label }}" class="form-label">Department *</label>
                        {{ doctor_form.department|add_class:"form-select" }}
                        {% if doctor_form.department.errors %}
                            <div class="text-danger">{{ doctor_form.department.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="{{ doctor_form.license_number.id_for_label }}" class="form-label">License Number *</label>
                        {{ doctor_form.license_number|add_class:"form-control" }}
                        {% if doctor_form.license_number.errors %}
                            <div class="text-danger">{{ doctor_form.license_number.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="{{ doctor_form.experience.id_for_label }}" class="form-label">Experience *</label>
                        {{ doctor_form.experience|add_class:"form-select" }}
                        {% if doctor_form.experience.errors %}
                            <div class="text-danger">{{ doctor_form.experience.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="{{ doctor_form.qualification.id_for_label }}" class="form-label">Qualification *</label>
                        {{ doctor_form.qualification|add_class:"form-control" }}
                        {% if doctor_form.qualification.errors %}
                            <div class="text-danger">{{ doctor_form.qualification.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="{{ doctor_form.consultation_fee.id_for_label }}" class="form-label">Consultation Fee *</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            {{ doctor_form.consultation_fee|add_class:"form-control" }}
                        </div>
                        {% if doctor_form.consultation_fee.errors %}
                            <div class="text-danger">{{ doctor_form.consultation_fee.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-12 mb-3">
                        <label for="{{ doctor_form.bio.id_for_label }}" class="form-label">Bio</label>
                        {{ doctor_form.bio|add_class:"form-control" }}
                        {% if doctor_form.bio.errors %}
                            <div class="text-danger">{{ doctor_form.bio.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="{{ doctor_form.signature.id_for_label }}" class="form-label">Signature</label>
                        {{ doctor_form.signature|add_class:"form-control" }}
                        {% if doctor_form.signature.errors %}
                            <div class="text-danger">{{ doctor_form.signature.errors }}</div>
                        {% endif %}
                        {% if doctor.signature %}
                            <div class="mt-2">
                                <img src="{{ doctor.signature.url }}" alt="Signature" style="max-width: 200px; max-height: 100px;">
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="form-check mt-4">
                            {{ doctor_form.available_for_appointments }}
                            <label class="form-check-label" for="{{ doctor_form.available_for_appointments.id_for_label }}">
                                Available for Appointments
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i> Save Changes
                    </button>
                    <a href="{% url 'doctors:manage_doctors' %}" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
