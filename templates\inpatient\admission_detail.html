{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">{{ title }}</h4>
                <div>
                    {% if admission.status == 'admitted' %}
                        <a href="{% url 'inpatient:edit_admission' admission.id %}" class="btn btn-light me-2">
                            <i class="fas fa-edit me-1"></i> Edit Admission
                        </a>
                        <a href="{% url 'inpatient:discharge_patient' admission.id %}" class="btn btn-light">
                            <i class="fas fa-procedures me-1"></i> Discharge Patient
                        </a>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5 class="border-bottom pb-2 mb-3">Patient Information</h5>
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">Patient Name</th>
                                <td>
                                    <a href="{% url 'patients:detail' admission.patient.id %}">
                                        {{ admission.patient.get_full_name }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <th>Patient ID</th>
                                <td>{{ admission.patient.patient_id }}</td>
                            </tr>
                            <tr>
                                <th>Gender</th>
                                <td>{{ admission.patient.get_gender_display }}</td>
                            </tr>
                            <tr>
                                <th>Age</th>
                                <td>{{ admission.patient.age }} years</td>
                            </tr>
                            <tr>
                                <th>Contact</th>
                                <td>{{ admission.patient.phone }}</td>
                            </tr>
                            <tr>
                                <th>Blood Group</th>
                                <td>{{ admission.patient.blood_group }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h5 class="border-bottom pb-2 mb-3">Admission Details</h5>
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">Admission Date</th>
                                <td>{{ admission.admission_date|date:"M d, Y H:i" }}</td>
                            </tr>
                            <tr>
                                <th>Ward/Bed</th>
                                <td>
                                    {% if admission.bed %}
                                        <a href="{% url 'inpatient:ward_detail' admission.bed.ward.id %}">
                                            {{ admission.bed.ward.name }}
                                        </a>
                                        / {{ admission.bed.bed_number }}
                                    {% else %}
                                        <span class="text-muted">Not assigned</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Attending Doctor</th>
                                <td>{{ admission.attending_doctor.get_full_name }}</td>
                            </tr>
                            <tr>
                                <th>Status</th>
                                <td>
                                    {% if admission.status == 'admitted' %}
                                        <span class="badge bg-success">Admitted</span>
                                    {% elif admission.status == 'discharged' %}
                                        <span class="badge bg-info">Discharged</span>
                                    {% elif admission.status == 'transferred' %}
                                        <span class="badge bg-warning">Transferred</span>
                                    {% elif admission.status == 'deceased' %}
                                        <span class="badge bg-danger">Deceased</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Duration</th>
                                <td>{{ admission.get_duration }} days</td>
                            </tr>
                            {% if admission.discharge_date %}
                                <tr>
                                    <th>Discharge Date</th>
                                    <td>{{ admission.discharge_date|date:"M d, Y H:i" }}</td>
                                </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>

                <!-- Add Prescription Button for Admitted Patients -->
                {% if admission.status == 'admitted' %}
                <div class="mb-4">
                    <a href="{% url 'pharmacy:create_prescription' %}?patient={{ admission.patient.id }}&doctor={{ admission.attending_doctor.id }}&diagnosis={{ admission.diagnosis|urlencode }}" class="btn btn-success">
                        <i class="fas fa-prescription"></i> Write Prescription
                    </a>
                </div>
                {% endif %}

                <div class="row mb-4">
                    <div class="col-md-12">
                        <h5 class="border-bottom pb-2 mb-3">Medical Information</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Diagnosis</h6>
                                    </div>
                                    <div class="card-body">
                                        {{ admission.diagnosis|linebreaks }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Reason for Admission</h6>
                                    </div>
                                    <div class="card-body">
                                        {{ admission.reason_for_admission|linebreaks }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Admission Notes</h6>
                                    </div>
                                    <div class="card-body">
                                        {{ admission.admission_notes|default:"No admission notes"|linebreaks }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Discharge Notes</h6>
                                    </div>
                                    <div class="card-body">
                                        {{ admission.discharge_notes|default:"No discharge notes"|linebreaks }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Prescriptions List for this Patient -->
                <div class="mt-5">
                    <h5 class="border-bottom pb-2 mb-3">Prescriptions</h5>
                    {% if prescriptions and prescriptions.exists %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Doctor</th>
                                        <th>Diagnosis</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for prescription in prescriptions %}
                                    <tr>
                                        <td>{{ prescription.prescription_date|date:"M d, Y" }}</td>
                                        <td>{{ prescription.doctor.get_full_name }}</td>
                                        <td>{{ prescription.diagnosis|default:"-" }}</td>
                                        <td>
                                            {% if prescription.status == 'pending' %}
                                                <span class="badge bg-warning">Pending</span>
                                            {% elif prescription.status == 'approved' %}
                                                <span class="badge bg-info">Approved</span>
                                            {% elif prescription.status == 'dispensed' %}
                                                <span class="badge bg-success">Dispensed</span>
                                            {% elif prescription.status == 'partially_dispensed' %}
                                                <span class="badge bg-primary">Partially Dispensed</span>
                                            {% elif prescription.status == 'cancelled' %}
                                                <span class="badge bg-danger">Cancelled</span>
                                            {% elif prescription.status == 'on_hold' %}
                                                <span class="badge bg-secondary">On Hold</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{% url 'pharmacy:prescription_detail' prescription.id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                            <a href="{% url 'pharmacy:print_prescription' prescription.id %}" class="btn btn-sm btn-outline-info" target="_blank">
                                                <i class="fas fa-print"></i> Print
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            No prescriptions found for this patient.
                        </div>
                    {% endif %}
                </div>

                <!-- Tabs for Daily Rounds and Nursing Notes -->
                <ul class="nav nav-tabs" id="myTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="rounds-tab" data-bs-toggle="tab" data-bs-target="#rounds" type="button" role="tab" aria-controls="rounds" aria-selected="true">
                            Doctor Rounds ({{ daily_rounds.count }})
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="notes-tab" data-bs-toggle="tab" data-bs-target="#notes" type="button" role="tab" aria-controls="notes" aria-selected="false">
                            Nursing Notes ({{ nursing_notes.count }})
                        </button>
                    </li>
                </ul>
                <div class="tab-content" id="myTabContent">
                    <!-- Doctor Rounds Tab -->
                    <div class="tab-pane fade show active" id="rounds" role="tabpanel" aria-labelledby="rounds-tab">
                        <div class="card border-top-0 rounded-top-0">
                            <div class="card-body">
                                {% if admission.status == 'admitted' %}
                                    <div class="mb-4">
                                        <h5 class="border-bottom pb-2 mb-3">Add Doctor Round</h5>
                                        <form method="post">
                                            {% csrf_token %}
                                            <input type="hidden" name="add_round" value="1">

                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="{{ round_form.date_time.id_for_label }}" class="form-label">Date & Time</label>
                                                        {{ round_form.date_time|add_class:"form-control" }}
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="{{ round_form.doctor.id_for_label }}" class="form-label">Doctor</label>
                                                        {{ round_form.doctor|add_class:"form-select select2" }}
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row mb-3">
                                                <div class="col-md-12">
                                                    <div class="form-group">
                                                        <label for="{{ round_form.notes.id_for_label }}" class="form-label">Notes</label>
                                                        {{ round_form.notes|add_class:"form-control" }}
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row mb-3">
                                                <div class="col-md-12">
                                                    <div class="form-group">
                                                        <label for="{{ round_form.treatment_instructions.id_for_label }}" class="form-label">Treatment Instructions</label>
                                                        {{ round_form.treatment_instructions|add_class:"form-control" }}
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="{{ round_form.medication_instructions.id_for_label }}" class="form-label">Medication Instructions</label>
                                                        {{ round_form.medication_instructions|add_class:"form-control" }}
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="{{ round_form.diet_instructions.id_for_label }}" class="form-label">Diet Instructions</label>
                                                        {{ round_form.diet_instructions|add_class:"form-control" }}
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="text-end">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-save me-1"></i> Save Round
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                {% endif %}

                                <h5 class="border-bottom pb-2 mb-3">Doctor Rounds History</h5>
                                {% if daily_rounds %}
                                    <div class="timeline">
                                        {% for round in daily_rounds %}
                                            <div class="timeline-item">
                                                <div class="timeline-marker"></div>
                                                <div class="timeline-content">
                                                    <div class="card mb-3">
                                                        <div class="card-header bg-light d-flex justify-content-between">
                                                            <h6 class="mb-0">{{ round.date_time|date:"M d, Y H:i" }}</h6>
                                                            <span>Dr. {{ round.doctor.get_full_name }}</span>
                                                        </div>
                                                        <div class="card-body">
                                                            <h6>Notes:</h6>
                                                            <p>{{ round.notes|linebreaks }}</p>

                                                            {% if round.treatment_instructions %}
                                                                <h6>Treatment Instructions:</h6>
                                                                <p>{{ round.treatment_instructions|linebreaks }}</p>
                                                            {% endif %}

                                                            {% if round.medication_instructions %}
                                                                <h6>Medication Instructions:</h6>
                                                                <p>{{ round.medication_instructions|linebreaks }}</p>
                                                            {% endif %}

                                                            {% if round.diet_instructions %}
                                                                <h6>Diet Instructions:</h6>
                                                                <p>{{ round.diet_instructions|linebreaks }}</p>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        No doctor rounds recorded yet.
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Nursing Notes Tab -->
                    <div class="tab-pane fade" id="notes" role="tabpanel" aria-labelledby="notes-tab">
                        <div class="card border-top-0 rounded-top-0">
                            <div class="card-body">
                                {% if admission.status == 'admitted' %}
                                    <div class="mb-4">
                                        <h5 class="border-bottom pb-2 mb-3">Add Nursing Note</h5>
                                        <form method="post">
                                            {% csrf_token %}
                                            <input type="hidden" name="add_note" value="1">

                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="{{ note_form.date_time.id_for_label }}" class="form-label">Date & Time</label>
                                                        {{ note_form.date_time|add_class:"form-control" }}
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="{{ note_form.nurse.id_for_label }}" class="form-label">Nurse</label>
                                                        {{ note_form.nurse|add_class:"form-select select2" }}
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row mb-3">
                                                <div class="col-md-12">
                                                    <div class="form-group">
                                                        <label for="{{ note_form.notes.id_for_label }}" class="form-label">Notes</label>
                                                        {{ note_form.notes|add_class:"form-control" }}
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="{{ note_form.vital_signs.id_for_label }}" class="form-label">Vital Signs</label>
                                                        {{ note_form.vital_signs|add_class:"form-control" }}
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="{{ note_form.medication_given.id_for_label }}" class="form-label">Medication Given</label>
                                                        {{ note_form.medication_given|add_class:"form-control" }}
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="text-end">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-save me-1"></i> Save Note
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                {% endif %}

                                <h5 class="border-bottom pb-2 mb-3">Nursing Notes History</h5>
                                {% if nursing_notes %}
                                    <div class="timeline">
                                        {% for note in nursing_notes %}
                                            <div class="timeline-item">
                                                <div class="timeline-marker"></div>
                                                <div class="timeline-content">
                                                    <div class="card mb-3">
                                                        <div class="card-header bg-light d-flex justify-content-between">
                                                            <h6 class="mb-0">{{ note.date_time|date:"M d, Y H:i" }}</h6>
                                                            <span>Nurse {{ note.nurse.get_full_name }}</span>
                                                        </div>
                                                        <div class="card-body">
                                                            <h6>Notes:</h6>
                                                            <p>{{ note.notes|linebreaks }}</p>

                                                            {% if note.vital_signs %}
                                                                <h6>Vital Signs:</h6>
                                                                <p>{{ note.vital_signs|linebreaks }}</p>
                                                            {% endif %}

                                                            {% if note.medication_given %}
                                                                <h6>Medication Given:</h6>
                                                                <p>{{ note.medication_given|linebreaks }}</p>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        No nursing notes recorded yet.
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <a href="{% url 'inpatient:admissions' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Admissions
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline:before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 4px;
        background: #e9ecef;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 30px;
    }

    .timeline-marker {
        position: absolute;
        left: -34px;
        top: 0;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #007bff;
        border: 3px solid #fff;
    }

    .timeline-content {
        padding-bottom: 10px;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
    });
</script>
{% endblock %}
