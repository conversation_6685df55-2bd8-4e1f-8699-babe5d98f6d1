{% extends 'base.html' %}
{% load static %}
{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="mb-0">{{ dashboard.name }}</h1>
        <span class="badge bg-info">Widgets: {{ widget_count }} | Reports: {{ report_count }}</span>
    </div>
    {% if last_updated %}
    <div class="mb-3 text-muted">Last updated: {{ last_updated|date:'M d, Y H:i' }}</div>
    {% endif %}
    <div class="row">
        {% for widget in widgets %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <strong>{{ widget.title }}</strong>
                    <span class="badge bg-light text-dark float-end">{{ widget.widget_type|capfirst }}</span>
                </div>
                <div class="card-body">
                    {% if widget.error %}
                        <div class="alert alert-danger">{{ widget.error }}</div>
                    {% elif widget.result and widget.result.data %}
                        <div class="table-responsive mb-2">
                            <table class="table table-sm table-bordered">
                                <thead>
                                    <tr>
                                        {% for col in widget.result.columns %}
                                            <th>{{ col }}</th>
                                        {% endfor %}
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for row in widget.result.rows|slice:':5' %}
                                        <tr>
                                            {% for val in row %}
                                                <td>{{ val }}</td>
                                            {% endfor %}
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% if widget.chart %}
                        <img src="data:image/png;base64,{{ widget.chart }}" class="img-fluid" alt="Chart">
                        {% endif %}
                    {% else %}
                        <div class="alert alert-info mb-0">No data available.</div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-secondary text-white">Recent Audit Logs</div>
                <div class="card-body p-2">
                    {% if audit_logs %}
                        <ul class="list-group list-group-flush">
                            {% for log in audit_logs %}
                                <li class="list-group-item small">
                                    <strong>{{ log.user.get_full_name|default:log.user.username }}</strong> - {{ log.action }} ({{ log.timestamp|date:'SHORT_DATETIME_FORMAT' }})
                                    <div class="text-muted">{{ log.details }}</div>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <div class="alert alert-info mb-0">No recent audit logs.</div>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-light">Notifications</div>
                <div class="card-body p-2">
                    {% if user_notifications %}
                        <ul class="list-group list-group-flush">
                            {% for n in user_notifications %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    {{ n.message }}
                                    <span class="badge bg-secondary">{{ n.created_at|date:'M d, Y H:i' }}</span>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <div class="alert alert-info mb-0">No notifications.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
