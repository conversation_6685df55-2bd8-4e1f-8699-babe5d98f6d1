{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6 offset-md-3 mb-4">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">{{ title }}</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Are you sure you want to delete bed <strong>{{ bed.bed_number }}</strong> from ward <strong>{{ bed.ward.name }}</strong>?
                    This action cannot be undone.
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h5 class="border-bottom pb-2 mb-3">Bed Information</h5>
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">Bed Number</th>
                                <td>{{ bed.bed_number }}</td>
                            </tr>
                            <tr>
                                <th>Ward</th>
                                <td>{{ bed.ward.name }}</td>
                            </tr>
                            <tr>
                                <th>Status</th>
                                <td>
                                    {% if not bed.is_active %}
                                        <span class="badge bg-secondary">Inactive</span>
                                    {% elif bed.is_occupied %}
                                        <span class="badge bg-danger">Occupied</span>
                                    {% else %}
                                        <span class="badge bg-success">Available</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Description</th>
                                <td>{{ bed.description|default:"No description" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'inpatient:ward_detail' bed.ward.id %}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i> Delete Bed
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
