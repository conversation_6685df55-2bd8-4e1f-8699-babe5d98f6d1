{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Reset Password - Hospital Management System{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card auth-form">
            <div class="card-body">
                <h2 class="card-title text-center mb-4">Reset Password</h2>
                <p class="text-center mb-4">Enter your phone number and we'll send instructions to reset your password to your registered email address.</p>

                <form method="post">
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <div class="mb-3">
                        <label for="{{ form.email.id_for_label }}" class="form-label">Phone Number</label>
                        {{ form.email|add_class:"form-control" }}
                        {% if form.email.errors %}
                            <div class="text-danger">
                                {{ form.email.errors }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Send Reset Link</button>
                    </div>

                    <div class="text-center mt-3">
                        <a href="{% url 'accounts:login' %}">Back to Login</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
