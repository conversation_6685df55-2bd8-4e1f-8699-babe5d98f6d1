{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Doctors - Hospital Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Our Doctors</h1>
    </div>

    <!-- Search and Filter Section -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Find a Doctor</h6>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="{{ search_form.name.id_for_label }}" class="form-label">Doctor Name</label>
                    {{ search_form.name|add_class:"form-control" }}
                </div>
                <div class="col-md-3">
                    <label for="{{ search_form.specialization.id_for_label }}" class="form-label">Specialization</label>
                    {{ search_form.specialization|add_class:"form-select" }}
                </div>
                <div class="col-md-3">
                    <label for="{{ search_form.department.id_for_label }}" class="form-label">Department</label>
                    {{ search_form.department|add_class:"form-select" }}
                </div>
                <div class="col-md-2">
                    <div class="form-check mt-4">
                        {{ search_form.available_only }}
                        <label class="form-check-label" for="{{ search_form.available_only.id_for_label }}">
                            Available Only
                        </label>
                    </div>
                </div>
                <div class="col-md-1">
                    <button type="submit" class="btn btn-primary mt-4">Search</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Doctors List -->
    <div class="row">
        {% if page_obj %}
            {% for doctor in page_obj %}
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100">
                        <div class="card-body">
                            <div class="text-center mb-3">
                                {% if doctor.user.profile.profile_picture %}
                                    <img src="{{ doctor.user.profile.profile_picture.url }}" alt="{{ doctor.get_full_name }}" class="img-profile rounded-circle" style="width: 100px; height: 100px; object-fit: cover;">
                                {% else %}
                                    <img src="/static/img/undraw_profile.svg" alt="{{ doctor.get_full_name }}" class="img-profile rounded-circle" style="width: 100px; height: 100px; object-fit: cover;">
                                {% endif %}
                            </div>
                            <div class="text-center">
                                <h5 class="font-weight-bold text-primary">{{ doctor.get_full_name }}</h5>
                                <p class="mb-1">{{ doctor.specialization.name }}</p>
                                <p class="mb-1">{{ doctor.department.name }}</p>
                                <div class="mb-2">
                                    {% if doctor.avg_rating %}
                                        <div class="small text-warning">
                                            {% for i in "12345" %}
                                                {% if forloop.counter <= doctor.avg_rating|floatformat:"0" %}
                                                    <i class="fas fa-star"></i>
                                                {% elif forloop.counter <= doctor.avg_rating|add:"0.5"|floatformat:"0" %}
                                                    <i class="fas fa-star-half-alt"></i>
                                                {% else %}
                                                    <i class="far fa-star"></i>
                                                {% endif %}
                                            {% endfor %}
                                            <span class="text-muted ml-1">({{ doctor.review_count }})</span>
                                        </div>
                                    {% else %}
                                        <div class="small text-muted">No reviews yet</div>
                                    {% endif %}
                                </div>
                                <a href="{% url 'doctors:doctor_detail' doctor.id %}" class="btn btn-primary btn-sm">View Profile</a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="card shadow mb-4">
                    <div class="card-body text-center py-5">
                        <h5 class="text-gray-500">No doctors found matching your criteria</h5>
                        <p>Try adjusting your search filters</p>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
        <div class="d-flex justify-content-center mt-4">
            <nav aria-label="Page navigation">
                <ul class="pagination">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    {% endif %}
</div>
{% endblock %}
