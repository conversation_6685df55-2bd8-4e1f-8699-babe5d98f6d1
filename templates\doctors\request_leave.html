{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Request Leave - Hospital Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Request Leave</h1>
        <a href="{% url 'doctors:doctor_profile' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left mr-1"></i> Back to Profile
        </a>
    </div>

    <div class="row">
        <!-- Request Leave Form -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">New Leave Request</h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.start_date.id_for_label }}" class="form-label">Start Date *</label>
                            {{ form.start_date|add_class:"form-control" }}
                            {% if form.start_date.errors %}
                                <div class="text-danger">{{ form.start_date.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.end_date.id_for_label }}" class="form-label">End Date *</label>
                            {{ form.end_date|add_class:"form-control" }}
                            {% if form.end_date.errors %}
                                <div class="text-danger">{{ form.end_date.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.reason.id_for_label }}" class="form-label">Reason *</label>
                            {{ form.reason|add_class:"form-control" }}
                            {% if form.reason.errors %}
                                <div class="text-danger">{{ form.reason.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane mr-1"></i> Submit Request
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Leave Requests List -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">My Leave Requests</h6>
                </div>
                <div class="card-body">
                    {% if leaves %}
                        <div class="table-responsive">
                            <table class="table table-bordered" id="leaveRequestsTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Start Date</th>
                                        <th>End Date</th>
                                        <th>Duration</th>
                                        <th>Reason</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for leave in leaves %}
                                        <tr>
                                            <td>{{ leave.start_date|date:"M d, Y" }}</td>
                                            <td>{{ leave.end_date|date:"M d, Y" }}</td>
                                            <td>{{ leave.end_date|timeuntil:leave.start_date }}</td>
                                            <td>{{ leave.reason|truncatechars:50 }}</td>
                                            <td>
                                                {% if leave.status == 'pending' %}
                                                    <span class="badge bg-warning text-dark">Pending</span>
                                                {% elif leave.status == 'approved' %}
                                                    <span class="badge bg-success text-white">Approved</span>
                                                {% elif leave.status == 'rejected' %}
                                                    <span class="badge bg-danger text-white">Rejected</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if leave.status == 'pending' %}
                                                    <form method="post" action="{% url 'doctors:cancel_leave' leave.id %}">
                                                        {% csrf_token %}
                                                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to cancel this leave request?');">
                                                            <i class="fas fa-times"></i> Cancel
                                                        </button>
                                                    </form>
                                                {% else %}
                                                    <span class="text-muted">No actions available</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">No leave requests found.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#leaveRequestsTable').DataTable({
            "order": [[0, "desc"]]
        });
    });
</script>
{% endblock %}
