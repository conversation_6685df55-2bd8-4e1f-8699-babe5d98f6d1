{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Medication Inventory - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Medication Inventory</h4>
                <div>
                    <a href="{% url 'pharmacy:add_medication' %}" class="btn btn-light me-2">
                        <i class="fas fa-plus"></i> Add Medication
                    </a>
                    <a href="{% url 'pharmacy:manage_categories' %}" class="btn btn-light me-2">
                        <i class="fas fa-tags"></i> Categories
                    </a>
                    <a href="{% url 'pharmacy:manage_suppliers' %}" class="btn btn-light">
                        <i class="fas fa-truck"></i> Suppliers
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if low_stock_alerts or expired_alerts %}
                    <div class="mb-3">
                        {% if low_stock_alerts %}
                            <div class="alert alert-warning">
                                <strong>Low Stock Alert:</strong>
                                {% for med in low_stock_alerts %}
                                    <span class="badge bg-warning text-dark">{{ med.name }} ({{ med.stock_quantity }})</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                        {% if expired_alerts %}
                            <div class="alert alert-danger">
                                <strong>Expired Medication Alert:</strong>
                                {% for med in expired_alerts %}
                                    <span class="badge bg-danger">{{ med.name }} (expired {{ med.expiry_date }})</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                {% endif %}
                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">Total Medications</h5>
                                <h2 class="mb-0">{{ total_medications }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">In Stock</h5>
                                <h2 class="mb-0">{{ in_stock_count }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">Low Stock</h5>
                                <h2 class="mb-0">{{ low_stock_count }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">Out of Stock</h5>
                                <h2 class="mb-0">{{ out_of_stock_count }}</h2>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search and Filter Form -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="get" class="row g-3">
                            <div class="col-md-4">
                                <label for="{{ search_form.search.id_for_label }}" class="form-label">Search</label>
                                {{ search_form.search|add_class:"form-control" }}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ search_form.category.id_for_label }}" class="form-label">Category</label>
                                {{ search_form.category|add_class:"form-select" }}
                            </div>
                            <div class="col-md-2">
                                <label for="{{ search_form.stock_status.id_for_label }}" class="form-label">Stock Status</label>
                                {{ search_form.stock_status|add_class:"form-select" }}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ search_form.is_active.id_for_label }}" class="form-label">Status</label>
                                {{ search_form.is_active|add_class:"form-select" }}
                            </div>
                            <div class="col-12 d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Search
                                </button>
                                <a href="{% url 'pharmacy:inventory' %}" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> Reset
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Medications Table -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Generic Name</th>
                                <th>Category</th>
                                <th>Dosage Form</th>
                                <th>Strength</th>
                                <th>Stock</th>
                                <th>Price</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for medication in page_obj %}
                                <tr class="{% if medication.stock_quantity == 0 %}table-danger{% elif medication.stock_quantity <= medication.reorder_level %}table-warning{% endif %}">
                                    <td>{{ medication.id }}</td>
                                    <td>
                                        <a href="{% url 'pharmacy:medication_detail' medication.id %}">
                                            {{ medication.name }}
                                        </a>
                                    </td>
                                    <td>{{ medication.generic_name }}</td>
                                    <td>{{ medication.category.name }}</td>
                                    <td>{{ medication.dosage_form }}</td>
                                    <td>{{ medication.strength }}</td>
                                    <td>
                                        {% if medication.stock_quantity == 0 %}
                                            <span class="badge bg-danger">Out of Stock</span>
                                        {% elif medication.stock_quantity <= medication.reorder_level %}
                                            <span class="badge bg-warning">Low Stock ({{ medication.stock_quantity }})</span>
                                        {% else %}
                                            <span class="badge bg-success">{{ medication.stock_quantity }}</span>
                                        {% endif %}
                                    </td>
                                    <td>₦{{ medication.price }}</td>
                                    <td>
                                        {% if medication.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-danger">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'pharmacy:medication_detail' medication.id %}" class="btn btn-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'pharmacy:edit_medication' medication.id %}" class="btn btn-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'pharmacy:delete_medication' medication.id %}" class="btn btn-danger" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% empty %}
                                <tr>
                                    <td colspan="10" class="text-center">No medications found.</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize select2 for category dropdown
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
    });
</script>
{% endblock %}
