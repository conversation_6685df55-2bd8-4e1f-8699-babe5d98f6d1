{% extends 'base.html' %}

{% block title %}Deactivate Staff - Hospital Management System{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">Deactivate Staff Member</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    You are about to deactivate the following staff member:
                </div>
                
                <div class="text-center mb-4">
                    {% if user_profile.profile_picture %}
                        <img src="{{ user_profile.profile_picture.url }}" alt="{{ user_profile.user.username }}" class="img-fluid rounded-circle" style="width: 100px; height: 100px;">
                    {% else %}
                        <i class="fas fa-user-circle fa-5x text-secondary"></i>
                    {% endif %}
                    <h5 class="mt-3">{{ user_profile.user.get_full_name }}</h5>
                    <p class="text-muted">{{ user_profile.get_role_display }} | {{ user_profile.employee_id }}</p>
                </div>
                
                <p class="mb-4">
                    This action will deactivate the staff member's account. They will no longer be able to log in to the system.
                    This action can be reversed later by an administrator.
                </p>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'accounts:staff_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-user-slash"></i> Deactivate Staff
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
