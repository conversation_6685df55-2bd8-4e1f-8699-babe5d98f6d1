<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Result - {{ result.test.name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 8px;
            color: #222;
            font-size: 12px;
        }
        .header {
            text-align: center;
            margin-bottom: 8px;
            border-bottom: 1px solid #333;
            padding-bottom: 4px;
        }
        .hospital-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 2px;
        }
        .hospital-info {
            font-size: 11px;
            margin-bottom: 2px;
        }
        .report-title {
            font-size: 13px;
            font-weight: bold;
            text-align: center;
            margin: 8px 0 6px 0;
            text-transform: uppercase;
        }
        .info-row {
            display: flex;
            margin-bottom: 2px;
        }
        .info-label {
            font-weight: bold;
            width: 90px;
            font-size: 11px;
        }
        .info-value {
            flex: 1;
            font-size: 11px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 4px;
            font-size: 11px;
        }
        th, td {
            border: 1px solid #bbb;
            padding: 3px 4px;
            text-align: left;
        }
        th {
            background-color: #f8f8f8;
        }
        .normal {
            color: #228B22;
        }
        .abnormal {
            color: #b22222;
            font-weight: bold;
        }
        .footer {
            margin-top: 12px;
            display: flex;
            justify-content: space-between;
            font-size: 11px;
        }
        .signature {
            width: 48%;
            text-align: center;
        }
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 18px;
            padding-top: 2px;
        }
        .print-info {
            font-size: 10px;
            color: #666;
            text-align: center;
            margin-top: 10px;
        }
        @media print {
            body {
                padding: 0;
                margin: 0;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="hospital-name">{{ hospital_name }}</div>
        <div class="hospital-info">{{ hospital_address }}</div>
        <div class="hospital-info">Phone: {{ hospital_phone }} | Email: {{ hospital_email }}</div>
    </div>
    <div class="report-title">Lab Test Result</div>
    <div class="info-row"><span class="info-label">Patient:</span><span class="info-value">{{ result.test_request.patient.get_full_name }}</span></div>
    <div class="info-row"><span class="info-label">ID:</span><span class="info-value">{{ result.test_request.patient.patient_id }}</span></div>
    <div class="info-row"><span class="info-label">Gender:</span><span class="info-value">{{ result.test_request.patient.get_gender_display }}</span></div>
    <div class="info-row"><span class="info-label">Age:</span><span class="info-value">{{ result.test_request.patient.age }} yrs</span></div>
    <div class="info-row"><span class="info-label">Doctor:</span><span class="info-value">Dr. {{ result.test_request.doctor.get_full_name }}</span></div>
    <div class="info-row"><span class="info-label">Test:</span><span class="info-value">{{ result.test.name }}</span></div>
    <div class="info-row"><span class="info-label">Cat.:</span><span class="info-value">{{ result.test.category.name }}</span></div>
    <div class="info-row"><span class="info-label">Sample:</span><span class="info-value">{{ result.test.sample_type }}</span></div>
    <div class="info-row"><span class="info-label">Req. Date:</span><span class="info-value">{{ result.test_request.request_date|date:"d/M/Y" }}</span></div>
    <div class="info-row"><span class="info-label">Coll. Date:</span><span class="info-value">{% if result.sample_collection_date %}{{ result.sample_collection_date|date:"d/M/Y H:i" }}{% else %}N/A{% endif %}</span></div>
    <div class="info-row"><span class="info-label">Result Date:</span><span class="info-value">{{ result.result_date|date:"d/M/Y" }}</span></div>
    <div style="margin: 6px 0 0 0;"><strong>Results:</strong></div>
    {% if parameters %}
        <table>
            <thead>
                <tr>
                    <th>Parameter</th>
                    <th>Result</th>
                    <th>Normal</th>
                    <th>Unit</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {% for parameter in parameters %}
                    <tr>
                        <td>{{ parameter.parameter.name }}</td>
                        <td>{{ parameter.value }}</td>
                        <td>{{ parameter.parameter.normal_range }}</td>
                        <td>{{ parameter.parameter.unit }}</td>
                        <td class="{% if parameter.is_normal %}normal{% else %}abnormal{% endif %}">
                            {% if parameter.is_normal %}N{% else %}A{% endif %}
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
        {% if result.notes %}
            <div style="margin-top: 6px; font-size: 11px;"><strong>Notes:</strong> {{ result.notes }}</div>
        {% endif %}
    {% else %}
        <p style="font-size: 11px;">No parameters recorded.</p>
    {% endif %}
    <div style="margin-top:10px; font-size:11px;">
        <strong>Lab Tech:</strong> {{ result.performed_by.get_full_name }}
        <br>
        <strong>Verified By:</strong> {% if result.verified_by %}{{ result.verified_by.get_full_name }}{% else %}<span style="color:#b22222">Not Verified</span>{% endif %}
    </div>
    <div class="footer">
        <div class="signature">
            <div class="signature-line">
                {{ result.performed_by.get_full_name }}<br>
                <small>Lab Tech</small>
            </div>
        </div>
        <div class="signature">
            <div class="signature-line">
                {% if result.verified_by %}
                    {{ result.verified_by.get_full_name }}<br>
                    <small>Verified</small>
                {% else %}
                    <br>
                    <small>Not Verified</small>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="print-info">
        Printed: {{ print_date|date:"d/M/Y H:i" }}<br>
        Computer-generated report.
    </div>
    <div class="no-print" style="text-align: center; margin-top: 10px;">
        <button onclick="window.print();" style="padding: 6px 16px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
            Print
        </button>
        <button onclick="window.close();" style="padding: 6px 16px; background-color: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; margin-left: 8px;">
            Close
        </button>
    </div>
</body>
</html>
