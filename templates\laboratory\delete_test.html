{% extends 'base.html' %}

{% block title %}Delete Test - Hospital Management System{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">Delete Test</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    You are about to deactivate the following test:
                </div>
                
                <div class="text-center mb-4">
                    <i class="fas fa-flask fa-5x text-danger mb-3"></i>
                    <h5>{{ test.name }}</h5>
                    <p class="text-muted">
                        {{ test.category.name }} - {{ test.sample_type }}
                    </p>
                    <p class="text-muted">
                        <strong>Price:</strong> ₦{{ test.price }}
                    </p>
                </div>
                
                <p class="mb-4">
                    This action will deactivate the test. The test will still exist in the database but will be marked as inactive and will not be available for new test requests.
                    This action can be reversed by editing the test and marking it as active again.
                </p>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'laboratory:tests' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> No, Go Back
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Yes, Deactivate Test
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
