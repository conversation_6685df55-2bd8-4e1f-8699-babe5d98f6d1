{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">{{ title }}</h4>
                <a href="{% url 'inpatient:create_admission' %}" class="btn btn-light">
                    <i class="fas fa-plus-circle me-1"></i> Admit Patient
                </a>
            </div>
            <div class="card-body">
                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h3>{{ total_admissions }}</h3>
                                <p class="mb-0">Total Admissions</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h3>{{ admitted_count }}</h3>
                                <p class="mb-0">Currently Admitted</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h3>{{ discharged_count }}</h3>
                                <p class="mb-0">Discharged</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h3>{{ deceased_count }}</h3>
                                <p class="mb-0">Deceased</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Analytics Row -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card text-center bg-secondary text-white">
                            <div class="card-body">
                                <h5 class="mb-2">Admissions by Role</h5>
                                <div class="d-flex flex-wrap justify-content-center">
                                    {% for rc in role_counts %}
                                        <span class="badge bg-light text-dark m-1">{{ rc.attending_doctor__roles__name|default:'(None)' }}: {{ rc.count }}</span>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card text-center bg-info text-dark">
                            <div class="card-body">
                                <h5 class="mb-2">Recent Audit Logs</h5>
                                {% if audit_logs %}
                                    <ul class="list-group">
                                        {% for log in audit_logs %}
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                <span>{{ log.timestamp|date:'M d, Y H:i' }} - {{ log.user.get_full_name|default:log.user.username }}: {{ log.action_type|capfirst }}</span>
                                                <span>{{ log.description }}</span>
                                            </li>
                                        {% endfor %}
                                    </ul>
                                {% else %}
                                    <div class="alert alert-info mb-0">No recent audit logs.</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Notifications Row -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card text-center bg-light">
                            <div class="card-body">
                                <h5 class="mb-2">Notifications</h5>
                                {% if user_notifications %}
                                    <ul class="list-group">
                                        {% for n in user_notifications %}
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                {{ n.message }}
                                                <span class="badge bg-secondary">{{ n.created_at|date:'M d, Y H:i' }}</span>
                                            </li>
                                        {% endfor %}
                                    </ul>
                                {% else %}
                                    <div class="alert alert-info mb-0">No notifications for you related to admissions.</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search and Filter Form -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Search & Filter</h5>
                    </div>
                    <div class="card-body">
                        <form method="get" class="row g-3">
                            <div class="col-md-4">
                                <label for="{{ search_form.search.id_for_label }}" class="form-label">Search</label>
                                {{ search_form.search|add_class:"form-control" }}
                            </div>
                            <div class="col-md-2">
                                <label for="{{ search_form.status.id_for_label }}" class="form-label">Status</label>
                                {{ search_form.status|add_class:"form-select" }}
                            </div>
                            <div class="col-md-2">
                                <label for="{{ search_form.date_from.id_for_label }}" class="form-label">From Date</label>
                                {{ search_form.date_from|add_class:"form-control" }}
                            </div>
                            <div class="col-md-2">
                                <label for="{{ search_form.date_to.id_for_label }}" class="form-label">To Date</label>
                                {{ search_form.date_to|add_class:"form-control" }}
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-1"></i> Search
                                </button>
                            </div>
                            <div class="col-md-6">
                                <label for="{{ search_form.doctor.id_for_label }}" class="form-label">Doctor</label>
                                {{ search_form.doctor|add_class:"form-select select2" }}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ search_form.ward.id_for_label }}" class="form-label">Ward</label>
                                {{ search_form.ward|add_class:"form-select" }}
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Admissions Table -->
                {% if page_obj %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Patient</th>
                                    <th>Admission Date</th>
                                    <th>Ward/Bed</th>
                                    <th>Doctor</th>
                                    <th>Status</th>
                                    <th>Duration</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for admission in page_obj %}
                                    <tr>
                                        <td>
                                            <a href="{% url 'patients:detail' admission.patient.id %}">
                                                {{ admission.patient.get_full_name }}
                                            </a>
                                        </td>
                                        <td>{{ admission.admission_date|date:"M d, Y H:i" }}</td>
                                        <td>
                                            {% if admission.bed %}
                                                <a href="{% url 'inpatient:ward_detail' admission.bed.ward.id %}">
                                                    {{ admission.bed.ward.name }}
                                                </a>
                                                / {{ admission.bed.bed_number }}
                                            {% else %}
                                                <span class="text-muted">Not assigned</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ admission.attending_doctor.get_full_name }}</td>
                                        <td>
                                            {% if admission.status == 'admitted' %}
                                                <span class="badge bg-success">Admitted</span>
                                            {% elif admission.status == 'discharged' %}
                                                <span class="badge bg-info">Discharged</span>
                                            {% elif admission.status == 'transferred' %}
                                                <span class="badge bg-warning">Transferred</span>
                                            {% elif admission.status == 'deceased' %}
                                                <span class="badge bg-danger">Deceased</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {{ admission.get_duration }} days
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{% url 'inpatient:admission_detail' admission.id %}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                                {% if admission.status == 'admitted' %}
                                                    <a href="{% url 'inpatient:edit_admission' admission.id %}" class="btn btn-sm btn-secondary">
                                                        <i class="fas fa-edit"></i> Edit
                                                    </a>
                                                    <a href="{% url 'inpatient:discharge_patient' admission.id %}" class="btn btn-sm btn-info">
                                                        <i class="fas fa-procedures"></i> Discharge
                                                    </a>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if page_obj.has_other_pages %}
                        <nav aria-label="Page navigation" class="mt-4">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                                            <span aria-hidden="true">&laquo;&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="First">
                                            <span aria-hidden="true">&laquo;&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                {% endif %}

                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                                            <span aria-hidden="true">&raquo;&raquo;</span>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Last">
                                            <span aria-hidden="true">&raquo;&raquo;</span>
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No admissions found matching your criteria.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
    });
</script>
{% endblock %}
