{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">{{ title }}</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.patient.id_for_label }}" class="form-label">Patient</label>
                            {{ form.patient|add_class:"form-select select2" }}
                            {% if form.patient.errors %}
                                <div class="text-danger">
                                    {{ form.patient.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.doctor.id_for_label }}" class="form-label">Doctor</label>
                            {{ form.doctor|add_class:"form-select select2" }}
                            {% if form.doctor.errors %}
                                <div class="text-danger">
                                    {{ form.doctor.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.request_date.id_for_label }}" class="form-label">Request Date</label>
                            {{ form.request_date|add_class:"form-control" }}
                            {% if form.request_date.errors %}
                                <div class="text-danger">
                                    {{ form.request_date.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.status.id_for_label }}" class="form-label">Status</label>
                            {{ form.status|add_class:"form-select" }}
                            {% if form.status.errors %}
                                <div class="text-danger">
                                    {{ form.status.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.priority.id_for_label }}" class="form-label">Priority</label>
                            {{ form.priority|add_class:"form-select" }}
                            {% if form.priority.errors %}
                                <div class="text-danger">
                                    {{ form.priority.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.tests.id_for_label }}" class="form-label">Tests</label>
                            {{ form.tests|add_class:"form-select select2" }}
                            {% if form.tests.errors %}
                                <div class="text-danger">
                                    {{ form.tests.errors }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Hold Ctrl (or Cmd on Mac) to select multiple tests.
                            </div>
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">Notes</label>
                            {{ form.notes|add_class:"form-control" }}
                            {% if form.notes.errors %}
                                <div class="text-danger">
                                    {{ form.notes.errors }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'laboratory:test_requests' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Test Requests
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Test Request
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize select2 for dropdowns
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
    });
</script>
{% endblock %}
