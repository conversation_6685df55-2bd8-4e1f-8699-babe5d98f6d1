{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">{{ title }}</h4>
                <a href="{% url 'pharmacy:inventory' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left"></i> Back to Inventory
                </a>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Add Supplier Form -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Add New Supplier</h5>
                            </div>
                            <div class="card-body">
                                <form method="post">
                                    {% csrf_token %}

                                    {% if form.non_field_errors %}
                                        <div class="alert alert-danger">
                                            {{ form.non_field_errors }}
                                        </div>
                                    {% endif %}

                                    <div class="mb-3">
                                        <label for="{{ form.name.id_for_label }}" class="form-label">Supplier Name</label>
                                        {{ form.name|add_class:"form-control" }}
                                        {% if form.name.errors %}
                                            <div class="text-danger">
                                                {{ form.name.errors }}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.contact_person.id_for_label }}" class="form-label">Contact Person</label>
                                        {{ form.contact_person|add_class:"form-control" }}
                                        {% if form.contact_person.errors %}
                                            <div class="text-danger">
                                                {{ form.contact_person.errors }}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.email.id_for_label }}" class="form-label">Email</label>
                                        {{ form.email|add_class:"form-control" }}
                                        {% if form.email.errors %}
                                            <div class="text-danger">
                                                {{ form.email.errors }}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.phone_number.id_for_label }}" class="form-label">Phone Number</label>
                                        {{ form.phone_number|add_class:"form-control" }}
                                        {% if form.phone_number.errors %}
                                            <div class="text-danger">
                                                {{ form.phone_number.errors }}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.address.id_for_label }}" class="form-label">Address</label>
                                        {{ form.address|add_class:"form-control" }}
                                        {% if form.address.errors %}
                                            <div class="text-danger">
                                                {{ form.address.errors }}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.city.id_for_label }}" class="form-label">City</label>
                                            {{ form.city|add_class:"form-control" }}
                                            {% if form.city.errors %}
                                                <div class="text-danger">
                                                    {{ form.city.errors }}
                                                </div>
                                            {% endif %}
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.state.id_for_label }}" class="form-label">State</label>
                                            {{ form.state|add_class:"form-control" }}
                                            {% if form.state.errors %}
                                                <div class="text-danger">
                                                    {{ form.state.errors }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="row">
                                        <!-- Postal Code field removed as per requirements -->

                                        <div class="col-md-12 mb-3">
                                            <label for="{{ form.country.id_for_label }}" class="form-label">Country</label>
                                            {{ form.country|add_class:"form-control" }}
                                            {% if form.country.errors %}
                                                <div class="text-danger">
                                                    {{ form.country.errors }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            {{ form.is_active }}
                                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                                Active
                                            </label>
                                        </div>
                                        {% if form.is_active.errors %}
                                            <div class="text-danger">
                                                {{ form.is_active.errors }}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> Add Supplier
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Suppliers List -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Suppliers</h5>
                            </div>
                            <div class="card-body">
                                {% if suppliers %}
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Name</th>
                                                    <th>Contact Person</th>
                                                    <th>Phone</th>
                                                    <th>Email</th>
                                                    <th>Status</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for supplier in suppliers %}
                                                    <tr>
                                                        <td>{{ supplier.id }}</td>
                                                        <td>{{ supplier.name }}</td>
                                                        <td>{{ supplier.contact_person }}</td>
                                                        <td>{{ supplier.phone_number }}</td>
                                                        <td>{{ supplier.email }}</td>
                                                        <td>
                                                            {% if supplier.is_active %}
                                                                <span class="badge bg-success">Active</span>
                                                            {% else %}
                                                                <span class="badge bg-danger">Inactive</span>
                                                            {% endif %}
                                                        </td>
                                                        <td>
                                                            <div class="btn-group btn-group-sm">
                                                                <a href="{% url 'pharmacy:edit_supplier' supplier.id %}" class="btn btn-primary" title="Edit">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <a href="{% url 'pharmacy:delete_supplier' supplier.id %}" class="btn btn-danger" title="Delete">
                                                                    <i class="fas fa-trash"></i>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                {% else %}
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        No suppliers found. Add a new supplier to get started.
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
