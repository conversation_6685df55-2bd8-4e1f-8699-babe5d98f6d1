{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Change Password - Hospital Management System{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card auth-form">
            <div class="card-body">
                <h2 class="card-title text-center mb-4">Change Password</h2>
                
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <label for="{{ form.old_password.id_for_label }}" class="form-label">Current Password</label>
                        {{ form.old_password|add_class:"form-control" }}
                        {% if form.old_password.errors %}
                            <div class="text-danger">
                                {{ form.old_password.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.new_password1.id_for_label }}" class="form-label">New Password</label>
                        {{ form.new_password1|add_class:"form-control" }}
                        {% if form.new_password1.errors %}
                            <div class="text-danger">
                                {{ form.new_password1.errors }}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            {{ form.new_password1.help_text }}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.new_password2.id_for_label }}" class="form-label">Confirm New Password</label>
                        {{ form.new_password2|add_class:"form-control" }}
                        {% if form.new_password2.errors %}
                            <div class="text-danger">
                                {{ form.new_password2.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Change Password</button>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="{% url 'accounts:profile' %}">Back to Profile</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
