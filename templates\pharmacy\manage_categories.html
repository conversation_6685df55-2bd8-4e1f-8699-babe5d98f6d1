{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">{{ title }}</h4>
                <a href="{% url 'pharmacy:inventory' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left"></i> Back to Inventory
                </a>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Add Category Form -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Add New Category</h5>
                            </div>
                            <div class="card-body">
                                <form method="post">
                                    {% csrf_token %}
                                    
                                    {% if form.non_field_errors %}
                                        <div class="alert alert-danger">
                                            {{ form.non_field_errors }}
                                        </div>
                                    {% endif %}
                                    
                                    <div class="mb-3">
                                        <label for="{{ form.name.id_for_label }}" class="form-label">Category Name</label>
                                        {{ form.name|add_class:"form-control" }}
                                        {% if form.name.errors %}
                                            <div class="text-danger">
                                                {{ form.name.errors }}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                                        {{ form.description|add_class:"form-control" }}
                                        {% if form.description.errors %}
                                            <div class="text-danger">
                                                {{ form.description.errors }}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> Add Category
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Categories List -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Categories</h5>
                            </div>
                            <div class="card-body">
                                {% if categories %}
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Name</th>
                                                    <th>Description</th>
                                                    <th>Medications</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for category in categories %}
                                                    <tr>
                                                        <td>{{ category.id }}</td>
                                                        <td>{{ category.name }}</td>
                                                        <td>{{ category.description|truncatechars:50 }}</td>
                                                        <td>{{ category.medications.count }}</td>
                                                        <td>
                                                            <div class="btn-group btn-group-sm">
                                                                <a href="{% url 'pharmacy:edit_category' category.id %}" class="btn btn-primary" title="Edit">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <a href="{% url 'pharmacy:delete_category' category.id %}" class="btn btn-danger" title="Delete">
                                                                    <i class="fas fa-trash"></i>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                {% else %}
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        No categories found. Add a new category to get started.
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
