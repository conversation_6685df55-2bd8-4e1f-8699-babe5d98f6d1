{% extends 'base.html' %}
{% load billing_tags %}

{% block title %}Delete Invoice - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6 mx-auto">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">Delete Invoice</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> You are about to delete invoice #{{ invoice.invoice_number }}. This action cannot be undone.
                </div>
                
                <div class="mb-4">
                    <h5>Invoice Information</h5>
                    <p><strong>Invoice Number:</strong> {{ invoice.invoice_number }}</p>
                    <p><strong>Date:</strong> {{ invoice.created_at|date:"F d, Y" }}</p>
                    <p><strong>Patient:</strong> {{ invoice.patient.get_full_name }}</p>
                    <p><strong>Amount:</strong> {{ invoice.total_amount|currency }}</p>
                    <p><strong>Status:</strong> {{ invoice.status|payment_status_badge }}</p>
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'billing:detail' invoice.id %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i> Delete Invoice
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
