{% extends 'base.html' %}
{% load static %}

{% block title %}My Patients{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">My Patients</h1>
    </div>

    <!-- Search Form -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Search Patients</h6>
        </div>
        <div class="card-body">
            <form method="get" action="{% url 'consultations:patient_list' %}" class="form-inline">
                <div class="form-group mb-2 mr-2">
                    <input type="text" name="search" class="form-control" placeholder="Search by name, ID, or phone" value="{{ search_query }}">
                </div>
                <button type="submit" class="btn btn-primary mb-2">Search</button>
                {% if search_query %}
                <a href="{% url 'consultations:patient_list' %}" class="btn btn-secondary mb-2 ml-2">Clear</a>
                {% endif %}
            </form>
        </div>
    </div>

    <!-- Patients List -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Patient List</h6>
        </div>
        <div class="card-body">
            {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Patient ID</th>
                                <th>Name</th>
                                <th>Gender</th>
                                <th>Age</th>
                                <th>Phone</th>
                                <th>Last Visit</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for patient in page_obj %}
                            <tr>
                                <td>{{ patient.patient_id }}</td>
                                <td>{{ patient.get_full_name }}</td>
                                <td>{{ patient.get_gender_display }}</td>
                                <td>{{ patient.age }}</td>
                                <td>{{ patient.phone_number }}</td>
                                <td>
                                    {% with last_appointment=patient.appointments.all.0 %}
                                    {% if last_appointment %}
                                        {{ last_appointment.appointment_date|date:"F d, Y" }}
                                    {% else %}
                                        N/A
                                    {% endif %}
                                    {% endwith %}
                                </td>
                                <td>
                                    <a href="{% url 'consultations:patient_detail' patient.id %}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="{% url 'consultations:create_consultation' patient.id %}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-stethoscope"></i> Consult
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <div class="pagination justify-content-center mt-4">
                    <ul class="pagination">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for i in page_obj.paginator.page_range %}
                            {% if page_obj.number == i %}
                            <li class="page-item active">
                                <a class="page-link" href="#">{{ i }}</a>
                            </li>
                            {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ i }}</a>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
                {% endif %}
            {% else %}
                <p class="text-center">No patients found.</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
