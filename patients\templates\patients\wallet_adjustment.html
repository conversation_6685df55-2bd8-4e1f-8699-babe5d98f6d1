{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}
  {{ title }} | {{ block.super }}
{% endblock title %}

{% block page_title %}
  {{ title }}
{% endblock page_title %}

{% block breadcrumbs %}
  <li class="breadcrumb-item"><a href="{% url 'patients:list' %}">Patients</a></li>
  <li class="breadcrumb-item"><a href="{% url 'patients:detail' patient.id %}">{{ patient.get_full_name }}</a></li>
  <li class="breadcrumb-item"><a href="{% url 'patients:wallet_dashboard' patient.id %}">Wallet</a></li>
  <li class="breadcrumb-item active" aria-current="page">Wallet Adjustment</li>
{% endblock breadcrumbs %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Make Wallet Adjustment</h6>
            </div>
            <div class="card-body">
                <!-- Warning Alert -->
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> Administrative Action</h6>
                    <p class="mb-0">Wallet adjustments are administrative actions that should be used carefully. All adjustments are logged and audited.</p>
                </div>

                <!-- Patient Info -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-user"></i> Patient Information</h6>
                    <p class="mb-1"><strong>Name:</strong> {{ patient.get_full_name }}</p>
                    <p class="mb-1"><strong>Patient ID:</strong> {{ patient.patient_id }}</p>
                    <p class="mb-0"><strong>Current Wallet Balance:</strong> <span class="text-success font-weight-bold">₦{{ wallet.balance|floatformat:2 }}</span></p>
                </div>

                <!-- Adjustment Form -->
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.adjustment_type.label_tag }}
                                {{ form.adjustment_type }}
                                {% if form.adjustment_type.errors %}
                                    <div class="text-danger">
                                        {% for error in form.adjustment_type.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    Credit increases balance, Debit decreases balance
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.amount.label_tag }}
                                {{ form.amount }}
                                {% if form.amount.errors %}
                                    <div class="text-danger">
                                        {% for error in form.amount.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    Available balance: ₦{{ wallet.balance|floatformat:2 }}
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        {{ form.reason.label_tag }}
                        {{ form.reason }}
                        {% if form.reason.errors %}
                            <div class="text-danger">
                                {% for error in form.reason.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">
                            Provide a detailed reason for this adjustment (required for audit trail)
                        </small>
                    </div>

                    <!-- Form Errors -->
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                <p class="mb-0">{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- Adjustment Summary (Hidden by default, shown via JavaScript) -->
                    <div id="adjustment-summary" class="alert" style="display: none;">
                        <h6><i class="fas fa-edit"></i> Adjustment Summary</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Patient:</strong> {{ patient.get_full_name }}<br>
                                <strong>Current Balance:</strong> ₦{{ wallet.balance|floatformat:2 }}<br>
                                <strong>Balance After Adjustment:</strong> <span id="balance-after">₦0.00</span>
                            </div>
                            <div class="col-md-6">
                                <strong>Adjustment Type:</strong> <span id="adjustment-type">-</span><br>
                                <strong>Adjustment Amount:</strong> <span id="adjustment-amount">₦0.00</span><br>
                                <strong>Reason:</strong> <span id="adjustment-reason">-</span>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="form-group mt-4">
                        <button type="submit" class="btn btn-secondary" id="adjustment-btn" disabled>
                            <i class="fas fa-edit"></i> Make Adjustment
                        </button>
                        <a href="{% url 'patients:wallet_dashboard' patient.id %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Wallet Summary -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Wallet Summary</h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <i class="fas fa-wallet fa-3x text-gray-300 mb-3"></i>
                    <h4 class="text-primary">₦{{ wallet.balance|floatformat:2 }}</h4>
                    <p class="text-muted">Current Balance</p>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="text-success">
                            <i class="fas fa-arrow-up"></i>
                            <div class="font-weight-bold">₦{{ wallet.get_total_credits|floatformat:2 }}</div>
                            <small>Total Credits</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-danger">
                            <i class="fas fa-arrow-down"></i>
                            <div class="font-weight-bold">₦{{ wallet.get_total_debits|floatformat:2 }}</div>
                            <small>Total Debits</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'patients:add_funds_to_wallet' patient.id %}" class="btn btn-success btn-sm">
                        <i class="fas fa-plus"></i> Add Funds
                    </a>
                    <a href="{% url 'patients:wallet_withdrawal' patient.id %}" class="btn btn-warning btn-sm">
                        <i class="fas fa-minus"></i> Withdraw Funds
                    </a>
                    <a href="{% url 'patients:wallet_transfer' patient.id %}" class="btn btn-info btn-sm">
                        <i class="fas fa-exchange-alt"></i> Transfer Funds
                    </a>
                    <a href="{% url 'patients:wallet_refund' patient.id %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-undo"></i> Process Refund
                    </a>
                    <a href="{% url 'patients:wallet_transactions' patient.id %}" class="btn btn-dark btn-sm">
                        <i class="fas fa-list"></i> View Transactions
                    </a>
                </div>
            </div>
        </div>

        <!-- Adjustment Guidelines -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">Adjustment Guidelines</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-shield-alt text-primary"></i>
                        <small>Administrative privilege required</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-audit text-info"></i>
                        <small>All adjustments are audited</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-comment text-warning"></i>
                        <small>Detailed reason is mandatory</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-exclamation-triangle text-danger"></i>
                        <small>Cannot exceed available balance for debits</small>
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-receipt text-success"></i>
                        <small>Creates permanent transaction record</small>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for real-time adjustment validation and summary -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const adjustmentTypeSelect = document.getElementById('id_adjustment_type');
    const amountInput = document.getElementById('id_amount');
    const reasonInput = document.getElementById('id_reason');
    const adjustmentSummary = document.getElementById('adjustment-summary');
    const adjustmentBtn = document.getElementById('adjustment-btn');
    const currentBalance = {{ wallet.balance }};
    
    function updateAdjustmentSummary() {
        const adjustmentType = adjustmentTypeSelect.value;
        const amount = parseFloat(amountInput.value) || 0;
        const reason = reasonInput.value.trim();
        
        if (adjustmentType && amount > 0 && reason) {
            // Show summary
            adjustmentSummary.style.display = 'block';
            
            // Calculate new balance
            let newBalance;
            if (adjustmentType === 'credit') {
                newBalance = currentBalance + amount;
                adjustmentSummary.className = 'alert alert-success';
            } else {
                newBalance = currentBalance - amount;
                adjustmentSummary.className = 'alert alert-warning';
            }
            
            // Update summary values
            document.getElementById('balance-after').textContent = `₦${newBalance.toFixed(2)}`;
            document.getElementById('adjustment-type').textContent = adjustmentType === 'credit' ? 'Credit Adjustment' : 'Debit Adjustment';
            document.getElementById('adjustment-amount').textContent = `₦${amount.toFixed(2)}`;
            document.getElementById('adjustment-reason').textContent = reason.substring(0, 50) + (reason.length > 50 ? '...' : '');
            
            // Enable/disable adjustment button
            if (adjustmentType === 'debit' && amount > currentBalance) {
                adjustmentBtn.disabled = true;
                adjustmentBtn.className = 'btn btn-danger';
                adjustmentBtn.innerHTML = '<i class="fas fa-ban"></i> Insufficient Balance';
            } else {
                adjustmentBtn.disabled = false;
                if (adjustmentType === 'credit') {
                    adjustmentBtn.className = 'btn btn-success';
                    adjustmentBtn.innerHTML = '<i class="fas fa-plus"></i> Credit Adjustment';
                } else {
                    adjustmentBtn.className = 'btn btn-warning';
                    adjustmentBtn.innerHTML = '<i class="fas fa-minus"></i> Debit Adjustment';
                }
            }
        } else {
            adjustmentSummary.style.display = 'none';
            adjustmentBtn.disabled = true;
            adjustmentBtn.className = 'btn btn-secondary';
            adjustmentBtn.innerHTML = '<i class="fas fa-edit"></i> Make Adjustment';
        }
    }
    
    function validateAmount() {
        const adjustmentType = adjustmentTypeSelect.value;
        const amount = parseFloat(amountInput.value) || 0;
        
        // Remove existing feedback
        const existingFeedback = amountInput.parentNode.querySelector('.balance-feedback');
        if (existingFeedback) {
            existingFeedback.remove();
        }
        
        // Add new feedback
        if (amount > 0 && adjustmentType) {
            const feedback = document.createElement('small');
            feedback.className = 'form-text balance-feedback';
            
            let newBalance;
            if (adjustmentType === 'credit') {
                newBalance = currentBalance + amount;
                feedback.className += ' text-success';
                feedback.innerHTML = `<i class="fas fa-arrow-up"></i> Balance after credit: ₦${newBalance.toFixed(2)}`;
            } else {
                newBalance = currentBalance - amount;
                if (amount > currentBalance) {
                    feedback.className += ' text-danger';
                    feedback.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Insufficient balance for debit!';
                } else {
                    feedback.className += ' text-warning';
                    feedback.innerHTML = `<i class="fas fa-arrow-down"></i> Balance after debit: ₦${newBalance.toFixed(2)}`;
                }
            }
            
            amountInput.parentNode.appendChild(feedback);
        }
    }
    
    // Event listeners
    if (adjustmentTypeSelect) {
        adjustmentTypeSelect.addEventListener('change', function() {
            validateAmount();
            updateAdjustmentSummary();
        });
    }
    
    if (amountInput) {
        amountInput.addEventListener('input', function() {
            validateAmount();
            updateAdjustmentSummary();
        });
    }
    
    if (reasonInput) {
        reasonInput.addEventListener('input', updateAdjustmentSummary);
    }
});
</script>
{% endblock content %}
