{% extends 'base.html' %}
{% load billing_tags %}

{% block title %}Invoices for {{ patient.get_full_name }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Invoices for {{ patient.get_full_name }}</h4>
                <div>
                    <a href="{% url 'billing:create' %}?patient_id={{ patient.id }}" class="btn btn-light me-2">
                        <i class="fas fa-plus-circle me-1"></i> Create Invoice
                    </a>
                    <a href="{% url 'patients:detail' patient.id %}" class="btn btn-light">
                        <i class="fas fa-arrow-left me-1"></i> Back to Patient
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-4 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">Total Invoices</h5>
                                <h2 class="mb-0">{{ invoices.count }}</h2>
                                <p class="mb-0">{{ total_amount|currency }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">Paid</h5>
                                <h2 class="mb-0">{{ invoices.paid.count }}</h2>
                                <p class="mb-0">{{ paid_amount|currency }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">Pending</h5>
                                <h2 class="mb-0">{{ invoices.pending.count }}</h2>
                                <p class="mb-0">{{ pending_amount|currency }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Invoices Table -->
                {% if invoices %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Invoice #</th>
                                    <th>Date</th>
                                    <th>Due Date</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in invoices %}
                                    <tr>
                                        <td>{{ invoice.invoice_number }}</td>
                                        <td>{{ invoice.created_at|date:"M d, Y" }}</td>
                                        <td>{{ invoice.due_date|date:"M d, Y" }}</td>
                                        <td>{{ invoice.total_amount|currency }}</td>
                                        <td>{{ invoice.status|payment_status_badge }}</td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{% url 'billing:detail' invoice.id %}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                                <a href="{% url 'billing:print' invoice.id %}" class="btn btn-sm btn-secondary" target="_blank">
                                                    <i class="fas fa-print"></i> Print
                                                </a>
                                                {% if invoice.status != 'paid' %}
                                                    <a href="{% url 'billing:record_payment' invoice.id %}" class="btn btn-sm btn-success">
                                                        <i class="fas fa-money-bill"></i> Pay
                                                    </a>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No invoices found for this patient.
                    </div>
                {% endif %}
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3">
                    <a href="{% url 'patients:detail' patient.id %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Patient
                    </a>
                    <a href="{% url 'billing:create' %}?patient_id={{ patient.id }}" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-1"></i> Create New Invoice
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
