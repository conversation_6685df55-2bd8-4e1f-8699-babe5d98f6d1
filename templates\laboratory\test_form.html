{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">{{ title }}</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">Test Name</label>
                            {{ form.name|add_class:"form-control" }}
                            {% if form.name.errors %}
                                <div class="text-danger">
                                    {{ form.name.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.category.id_for_label }}" class="form-label">Category</label>
                            {{ form.category|add_class:"form-select select2" }}
                            {% if form.category.errors %}
                                <div class="text-danger">
                                    {{ form.category.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.price.id_for_label }}" class="form-label">Price (₦)</label>
                            {{ form.price|add_class:"form-control" }}
                            {% if form.price.errors %}
                                <div class="text-danger">
                                    {{ form.price.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.sample_type.id_for_label }}" class="form-label">Sample Type</label>
                            {{ form.sample_type|add_class:"form-control" }}
                            {% if form.sample_type.errors %}
                                <div class="text-danger">
                                    {{ form.sample_type.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.duration.id_for_label }}" class="form-label">Duration</label>
                            {{ form.duration|add_class:"form-control" }}
                            {% if form.duration.errors %}
                                <div class="text-danger">
                                    {{ form.duration.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.normal_range.id_for_label }}" class="form-label">Normal Range</label>
                            {{ form.normal_range|add_class:"form-control" }}
                            {% if form.normal_range.errors %}
                                <div class="text-danger">
                                    {{ form.normal_range.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.unit.id_for_label }}" class="form-label">Unit</label>
                            {{ form.unit|add_class:"form-control" }}
                            {% if form.unit.errors %}
                                <div class="text-danger">
                                    {{ form.unit.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                            {{ form.description|add_class:"form-control" }}
                            {% if form.description.errors %}
                                <div class="text-danger">
                                    {{ form.description.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.preparation_instructions.id_for_label }}" class="form-label">Preparation Instructions</label>
                            {{ form.preparation_instructions|add_class:"form-control" }}
                            {% if form.preparation_instructions.errors %}
                                <div class="text-danger">
                                    {{ form.preparation_instructions.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            <div class="form-check form-switch">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    Active
                                </label>
                            </div>
                            {% if form.is_active.errors %}
                                <div class="text-danger">
                                    {{ form.is_active.errors }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'laboratory:tests' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Tests
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Test
                        </button>
                    </div>
                </form>
                
                {% if test %}
                    <!-- Test Parameters Section -->
                    <div class="mt-5">
                        <h5 class="border-bottom pb-2 mb-3">Test Parameters</h5>
                        
                        <div class="row">
                            <div class="col-md-5">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Add Parameter</h6>
                                    </div>
                                    <div class="card-body">
                                        <form method="post">
                                            {% csrf_token %}
                                            <input type="hidden" name="add_parameter" value="1">
                                            
                                            {# Add prefix to parameter_form fields #}
                                            <div class="mb-3">
                                                <label for="{{ parameter_form.name.id_for_label }}" class="form-label">Parameter Name</label>
                                                {{ parameter_form.name|add_class:"form-control" }}
                                                {% if parameter_form.name.errors %}
                                                    <div class="text-danger">
                                                        {{ parameter_form.name.errors }}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="{{ parameter_form.normal_range.id_for_label }}" class="form-label">Normal Range</label>
                                                {{ parameter_form.normal_range|add_class:"form-control" }}
                                                {% if parameter_form.normal_range.errors %}
                                                    <div class="text-danger">
                                                        {{ parameter_form.normal_range.errors }}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="{{ parameter_form.unit.id_for_label }}" class="form-label">Unit</label>
                                                {{ parameter_form.unit|add_class:"form-control" }}
                                                {% if parameter_form.unit.errors %}
                                                    <div class="text-danger">
                                                        {{ parameter_form.unit.errors }}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="{{ parameter_form.order.id_for_label }}" class="form-label">Display Order</label>
                                                {{ parameter_form.order|add_class:"form-control" }}
                                                {% if parameter_form.order.errors %}
                                                    <div class="text-danger">
                                                        {{ parameter_form.order.errors }}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            
                                            <div class="d-grid">
                                                <button type="submit" class="btn btn-primary" name="add_parameter_submit"> {# Added name to submit button for clarity #}
                                                    <i class="fas fa-plus"></i> Add Parameter
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-7">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Parameters List</h6>
                                    </div>
                                    <div class="card-body">
                                        {% if parameters %}
                                            <div class="table-responsive">
                                                <table class="table table-hover">
                                                    <thead>
                                                        <tr>
                                                            <th>Order</th>
                                                            <th>Name</th>
                                                            <th>Normal Range</th>
                                                            <th>Unit</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for parameter in parameters %}
                                                            <tr>
                                                                <td>{{ parameter.order }}</td>
                                                                <td>{{ parameter.name }}</td>
                                                                <td>{{ parameter.normal_range }}</td>
                                                                <td>{{ parameter.unit }}</td>
                                                                <td>
                                                                    <a href="{% url 'laboratory:delete_parameter' parameter.id %}" class="btn btn-sm btn-danger">
                                                                        <i class="fas fa-trash"></i>
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                        {% else %}
                                            <div class="alert alert-info">
                                                <i class="fas fa-info-circle me-2"></i>
                                                No parameters have been added to this test yet.
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize select2 for category dropdown
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
    });
</script>
{% endblock %}
