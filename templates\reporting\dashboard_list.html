{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid mt-4 mb-4"> <!-- Added margin classes -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Dashboards</h1>
        <a href="{% url 'reporting:create_dashboard' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create Dashboard
        </a>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Filter Dashboards</h5>
                    <form method="get">
                        <div class="mb-3">
                            <label for="id_search" class="form-label">Search</label>
                            {{ search_form.search }}
                        </div>
                        <div class="mb-3">
                            <label for="id_is_public" class="form-label">Visibility</label>
                            {{ search_form.is_public }}
                        </div>
                        <button type="submit" class="btn btn-primary w-100">Apply Filters</button>
                    </form>
                </div>
            </div>
            <div class="card mt-3">
                <div class="card-body">
                    <h5 class="card-title">Dashboard Stats</h5>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Total Dashboards
                            <span class="badge bg-primary rounded-pill">{{ total_dashboards }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            My Dashboards
                            <span class="badge bg-primary rounded-pill">{{ my_dashboards_count }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Public Dashboards
                            <span class="badge bg-primary rounded-pill">{{ public_dashboards_count }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Default Dashboards
                            <span class="badge bg-primary rounded-pill">{{ default_dashboards_count }}</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-md-9">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Description</th>
                                    <th>Created By</th>
                                    <th>Visibility</th>
                                    <th>Default</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for dashboard in page_obj %}
                                <tr>
                                    <td>{{ dashboard.name }}</td>
                                    <td>{{ dashboard.description|truncatechars:50 }}</td>
                                    <td>{{ dashboard.created_by.get_full_name }}</td>
                                    <td>
                                        {% if dashboard.is_public %}
                                        <span class="badge bg-success">Public</span>
                                        {% else %}
                                        <span class="badge bg-secondary">Private</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if dashboard.is_default %}
                                        <span class="badge bg-primary">Default</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'reporting:dashboard' %}?id={{ dashboard.id }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if dashboard.created_by == request.user %}
                                            <a href="{% url 'reporting:edit_dashboard' dashboard.id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'reporting:delete_dashboard' dashboard.id %}" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">No dashboards found</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    {% if page_obj.has_other_pages %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.is_public %}&is_public={{ request.GET.is_public }}{% endif %}" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.is_public %}&is_public={{ request.GET.is_public }}{% endif %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.is_public %}&is_public={{ request.GET.is_public }}{% endif %}">{{ num }}</a>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.is_public %}&is_public={{ request.GET.is_public }}{% endif %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.is_public %}&is_public={{ request.GET.is_public }}{% endif %}" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
