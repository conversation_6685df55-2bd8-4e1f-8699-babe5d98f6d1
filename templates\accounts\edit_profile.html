{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}
    {{ page_title|default:"Edit Profile" }}
{% endblock %}

{% block content %}
<div class="container mt-5 mb-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-7">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">{{ page_title|default:"Edit Profile" }}</h4>
                </div>
                <div class="card-body p-4">
                    {% if profile_user_object.profile.profile_picture %}
                        <div class="text-center mb-4">
                            <img src="{{ profile_user_object.profile.profile_picture.url }}" 
                                 alt="Current Profile Picture for {{ profile_user_object.username }}" 
                                 class="img-fluid rounded-circle" 
                                 style="width: 120px; height: 120px; object-fit: cover; border: 3px solid #dee2e6;">
                        </div>
                    {% endif %}

                    <form method="post" enctype="multipart/form-data" novalidate>
                        {% csrf_token %}
                        
                        {% for field in form %}
                            {% if field.field.widget.input_type == 'checkbox' %}
                                <div class="mb-3 form-check">
                                    {{ field }}
                                    <label class="form-check-label" for="{{ field.id_for_label }}">{{ field.label }}</label>
                                    {% if field.help_text %}
                                        <small class="form-text text-muted d-block">{{ field.help_text }}</small>
                                    {% endif %}
                                    {% if field.errors %}
                                        {% for error in field.errors %}
                                            <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            {% else %}
                                <div class="mb-3">
                                    <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                    {{ field }}
                                    {% if field.help_text %}
                                        <small class="form-text text-muted">{{ field.help_text }}</small>
                                    {% endif %}
                                    {% if field.errors %}
                                        {% for error in field.errors %}
                                            <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            {% endif %}
                        {% endfor %}
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="{% url 'accounts:profile' %}?user_id={{ profile_user_object.id }}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-1"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{# If using icons, ensure Font Awesome is included in your base.html #}
{# <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"> #}
{% endblock %}