{% extends 'base.html' %}
{% load form_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">{{ title }}</h4>
            </div>
            <div class="card-body">
                {# Show non-field and field errors at the top #}
                {% if form.non_field_errors %}
                  <div class="alert alert-danger">
                    <ul>
                      {% for error in form.non_field_errors %}
                        <li>{{ error }}</li>
                      {% endfor %}
                    </ul>
                  </div>
                {% endif %}
                {% for field in form %}
                  {% if field.errors %}
                    <div class="alert alert-danger">
                      <strong>{{ field.label }}:</strong>
                      <ul>
                        {% for error in field.errors %}
                          <li>{{ error }}</li>
                        {% endfor %}
                      </ul>
                    </div>
                  {% endif %}
                {% endfor %}
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.patient.id_for_label }}" class="form-label">Patient</label>
                                {{ form.patient|add_class:"form-select select2" }}
                                {% if form.patient.errors %}
                                    <div class="text-danger">
                                        {{ form.patient.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.admission_date.id_for_label }}" class="form-label">Admission Date & Time</label>
                                {{ form.admission_date|add_class:"form-control" }}
                                {% if form.admission_date.errors %}
                                    <div class="text-danger">
                                        {{ form.admission_date.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.bed.id_for_label }}" class="form-label">Bed</label>
                                {{ form.bed|add_class:"form-select" }}
                                {% if form.bed.errors %}
                                    <div class="text-danger">
                                        {{ form.bed.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.attending_doctor.id_for_label }}" class="form-label">Attending Doctor</label>
                                {{ form.attending_doctor|add_class:"form-select select2" }}
                                {% if form.attending_doctor.errors %}
                                    <div class="text-danger">
                                        {{ form.attending_doctor.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="{{ form.diagnosis.id_for_label }}" class="form-label">Diagnosis</label>
                                {{ form.diagnosis|add_class:"form-control" }}
                                {% if form.diagnosis.errors %}
                                    <div class="text-danger">
                                        {{ form.diagnosis.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="{{ form.reason_for_admission.id_for_label }}" class="form-label">Reason for Admission</label>
                                {{ form.reason_for_admission|add_class:"form-control" }}
                                {% if form.reason_for_admission.errors %}
                                    <div class="text-danger">
                                        {{ form.reason_for_admission.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="{{ form.admission_notes.id_for_label }}" class="form-label">Admission Notes</label>
                                {{ form.admission_notes|add_class:"form-control" }}
                                {% if form.admission_notes.errors %}
                                    <div class="text-danger">
                                        {{ form.admission_notes.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    {% if form.instance.pk %}
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="{{ form.status.id_for_label }}" class="form-label">Status</label>
                                    {{ form.status|add_class:"form-select" }}
                                    {% if form.status.errors %}
                                        <div class="text-danger">
                                            {{ form.status.errors }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-between">
                                <a href="{% url 'inpatient:admissions' %}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i> Back to Admissions
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> 
                                    {% if form.instance.pk %}
                                        Update Admission
                                    {% else %}
                                        Admit Patient
                                    {% endif %}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
    });
</script>
{% endblock %}
