{% extends 'base.html' %}
{% block title %}User Management - Hospital Management System{% endblock %}

{% block extra_css %}
<style>
    /* Ensure the user dashboard is scrollable */
    .user-dashboard-content {
        height: calc(100vh - 140px);
        overflow-y: auto;
        padding-right: 15px;
    }

    /* Smooth scrolling */
    .user-dashboard-content {
        scroll-behavior: smooth;
    }

    /* Custom scrollbar for better UX */
    .user-dashboard-content::-webkit-scrollbar {
        width: 8px;
    }

    .user-dashboard-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .user-dashboard-content::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 4px;
    }

    .user-dashboard-content::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    /* Ensure table headers stay visible when scrolling */
    .table-responsive {
        position: relative;
    }

    .sticky-top {
        position: sticky;
        top: 0;
        z-index: 10;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid user-dashboard-content">
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Scrollable Content:</strong> This page is now fully scrollable. Use the scroll wheel or scroll bar to navigate through the user list. A scroll-to-top button will appear when you scroll down.
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <h2 class="mb-4">User Management</h2>
    <!-- Analytics Row -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center bg-primary text-white">
                <div class="card-body">
                    <h4>{{ active_count }}</h4>
                    <p class="mb-0">Active Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center bg-danger text-white">
                <div class="card-body">
                    <h4>{{ inactive_count }}</h4>
                    <p class="mb-0">Inactive Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card text-center bg-info text-dark"> <!-- Ensure text is readable, bg-info might need text-white or adjust -->
                <div class="card-body">
                    <h5 class="mb-2">Users by Role</h5>
                    <div class="d-flex flex-wrap justify-content-center">
                        {% for rc in role_counts %} {# role_counts is now a list of dicts {'name': ..., 'count': ...} #}
                            <span class="badge bg-secondary m-1">{{ rc.name }}: {{ rc.count }}</span>
                        {% empty %}
                            <p>No roles found or no users assigned to roles.</p>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Privilege Management Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-bolt me-2"></i>Privilege Management</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <a href="{% url 'accounts:create_staff' %}" class="btn btn-success w-100 mb-2">
                                <i class="fas fa-user-plus me-2"></i>Add New User
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'accounts:role_management' %}" class="btn btn-primary w-100 mb-2">
                                <i class="fas fa-user-shield me-2"></i>Manage Roles
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'accounts:permission_management' %}" class="btn btn-info w-100 mb-2">
                                <i class="fas fa-key me-2"></i>Permissions
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'accounts:create_role' %}" class="btn btn-outline-primary w-100 mb-2">
                                <i class="fas fa-plus me-2"></i>Create Role
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{% url 'accounts:audit_logs' %}" class="btn btn-warning w-100 mb-2">
                                <i class="fas fa-history me-2"></i>Audit Logs
                            </a>
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-outline-secondary w-100 mb-2" onclick="showBulkActions()">
                                <i class="fas fa-tasks me-2"></i>Bulk Actions
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <form method="get" class="row g-3 mb-3">
        <div class="col-md-3">
            <label for="search" class="form-label">Search</label>
            <input type="text" class="form-control" id="search" name="search" value="{{ search|default:'' }}" placeholder="Username, name, email, phone">
        </div>
        <div class="col-md-3">
            <label for="role" class="form-label">Role</label>
            <select class="form-select" id="role" name="role">
                <option value="">All Roles</option>
                {% for r_obj in roles %} {# 'roles' from context is a list of Role objects #}
                    <option value="{{ r_obj.name }}" {% if role == r_obj.name %}selected{% endif %}>{{ r_obj.name }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-3">
            <label for="is_active" class="form-label">Status</label>
            <select class="form-select" id="is_active" name="is_active">
                <option value="">All Statuses</option>
                <option value="true" {% if is_active == 'true' %}selected{% endif %}>Active</option>
                <option value="false" {% if is_active == 'false' %}selected{% endif %}>Inactive</option>
            </select>
        </div>
        <div class="col-md-3 d-flex align-items-end">
            <button type="submit" class="btn btn-primary w-100">
                <i class="fas fa-search me-1"></i> Filter
            </button>
        </div>
    </form>
    <div class="mb-3 text-end">
        <a href="?{% if search %}search={{ search }}&{% endif %}{% if role %}role={{ role }}&{% endif %}{% if is_active %}is_active={{ is_active }}&{% endif %}export=1" class="btn btn-outline-success">
            <i class="fas fa-file-csv"></i> Export CSV
        </a>
    </div>
    <form method="post">
        {% csrf_token %}
        <div class="row g-2 align-items-end mb-2">
            <div class="col-auto">
                <select name="bulk_action" class="form-select">
                    <option value="">Bulk Action</option>
                    <option value="activate">Activate</option>
                    <option value="deactivate">Deactivate</option>
                    <option value="assign_role">Assign Role</option>
                </select>
            </div>
            <div class="col-auto">
                <select name="assign_role" class="form-select">
                    <option value="">Select Role to Assign</option>
                    {% for r_obj in roles %} {# 'roles' from context is a list of Role objects #}
                        <option value="{{ r_obj.name }}">{{ r_obj.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-auto">
                <button type="submit" class="btn btn-warning">Apply</button>
            </div>
        </div>
        <div class="table-responsive" style="max-height: 60vh; overflow-y: auto;">
            <table class="table table-hover align-middle">
                <thead class="sticky-top bg-light">
                    <tr>
                        <th><input type="checkbox" id="select-all"></th>
                        <th>Username</th>
                        <th>Full Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Roles</th> {# Changed header from Role to Roles #}
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user_item in page_obj %} {# Renamed user to user_item to avoid conflict with request.user if used in template #}
                    <tr>
                        <td><input type="checkbox" name="selected_users" value="{{ user_item.id }}"></td>
                        <td>{{ user_item.username }}</td>
                        <td>{{ user_item.get_full_name|default:'-' }}</td>
                        <td>{{ user_item.email|default:'-' }}</td>
                        <td>{{ user_item.phone_number|default:'-' }}</td>
                        <td> {# Display user roles #}
                            {% for r in user_item.roles.all %}
                                <span class="badge bg-info text-dark me-1">{{ r.name }}</span> {# Ensure contrast for bg-info #}
                            {% empty %}
                                <span class="badge bg-light text-dark">-</span>
                            {% endfor %}
                        </td>
                        <td>
                            {% if user_item.is_active %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-danger">Inactive</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'accounts:profile' %}?user_id={{ user_item.id }}" class="btn btn-sm btn-outline-primary" title="View Profile">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'accounts:edit_profile' %}?user_id={{ user_item.id }}" class="btn btn-sm btn-outline-secondary" title="Edit Profile">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'accounts:user_privileges' user_item.id %}" class="btn btn-sm btn-outline-success" title="Manage Privileges">
                                    <i class="fas fa-user-cog"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center">No users found.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </form>
    {# accounts/user_dashboard.html (pagination part) #}

    {% if page_obj.has_other_pages %}
        <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
                {# Previous button #}
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if role %}&role={{ role }}{% endif %}{% if is_active %}&is_active={{ is_active }}{% endif %}">Previous</a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">Previous</span>
                    </li>
                {% endif %}

                {# Page numbers with ellipsis #}
                {% for i in elided_page_range %}
                    {% if i == page_obj.paginator.ELLIPSIS %}
                        <li class="page-item disabled"><span class="page-link">…</span></li>
                    {% else %}
                        <li class="page-item {% if page_obj.number == i %}active{% endif %}">
                            <a class="page-link" href="?page={{ i }}{% if search %}&search={{ search }}{% endif %}{% if role %}&role={{ role }}{% endif %}{% if is_active %}&is_active={{ is_active }}{% endif %}">{{ i }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {# Next button #}
                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if role %}&role={{ role }}{% endif %}{% if is_active %}&is_active={{ is_active }}{% endif %}">Next</a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">Next</span>
                    </li>
                {% endif %}
            </ul>
        </nav>
    {% endif %}

    <!-- Scroll to Top Button -->
    <button class="btn btn-primary scroll-to-top" id="scrollToTop" style="position: fixed; bottom: 20px; right: 20px; display: none; z-index: 1000; border-radius: 50%; width: 50px; height: 50px;">
        <i class="fas fa-arrow-up"></i>
    </button>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const selectAllCheckbox = document.getElementById('select-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                document.querySelectorAll('input[name="selected_users"]').forEach(checkbox => {
                    checkbox.checked = selectAllCheckbox.checked;
                });
            });
        }

        // Scroll to top functionality
        const scrollToTopBtn = document.getElementById('scrollToTop');
        const userDashboardContent = document.querySelector('.user-dashboard-content');

        if (userDashboardContent && scrollToTopBtn) {
            userDashboardContent.addEventListener('scroll', function() {
                if (userDashboardContent.scrollTop > 300) {
                    scrollToTopBtn.style.display = 'block';
                } else {
                    scrollToTopBtn.style.display = 'none';
                }
            });

            scrollToTopBtn.addEventListener('click', function() {
                userDashboardContent.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }
    });

    // Bulk actions functionality
    function showBulkActions() {
        const selectedUsers = document.querySelectorAll('input[name="selected_users"]:checked');
        if (selectedUsers.length === 0) {
            alert('Please select at least one user to perform bulk actions.');
            return;
        }

        const bulkActionSection = document.querySelector('.row.g-2.align-items-end.mb-2');
        bulkActionSection.style.display = bulkActionSection.style.display === 'none' ? 'flex' : 'none';
    }

    // Enhanced bulk action confirmation
    document.querySelector('form[method="post"]').addEventListener('submit', function(e) {
        const selectedUsers = document.querySelectorAll('input[name="selected_users"]:checked');
        const bulkAction = document.querySelector('select[name="bulk_action"]').value;

        if (selectedUsers.length === 0) {
            e.preventDefault();
            alert('Please select at least one user.');
            return;
        }

        if (!bulkAction) {
            e.preventDefault();
            alert('Please select a bulk action.');
            return;
        }

        const actionText = document.querySelector('select[name="bulk_action"] option:checked').textContent;
        const userCount = selectedUsers.length;

        if (!confirm(`Are you sure you want to ${actionText.toLowerCase()} ${userCount} user(s)?`)) {
            e.preventDefault();
        }
    });
</script>
{% endblock %}
